using System;
using System.IO;
using System.Threading.Tasks;

namespace DriverManagementSystem.Data
{
    /// <summary>
    /// إعدادات قاعدة البيانات - SQL Server مع حفظ في مجلد Data
    /// </summary>
    public static class DatabaseConfig
    {
        private static readonly string ConfigFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "db_config.txt");

        /// <summary>
        /// الحصول على connection string لـ SQL Server
        /// </summary>
        public static string GetConnectionString()
        {
            var settings = LoadSettings();

            // إذا كانت قاعدة البيانات محلية
            if (settings.UseLocalDatabase)
            {
                var dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                var dbPath = Path.Combine(dataFolder, $"{settings.DatabaseName}.mdf");

                // إنشاء مجلد Data إذا لم يكن موجوداً
                if (!Directory.Exists(dataFolder))
                {
                    Directory.CreateDirectory(dataFolder);
                }

                return $@"Server=(LocalDB)\MSSQLLocalDB;AttachDbFilename={dbPath};Database={settings.DatabaseName};Trusted_Connection=True;";
            }

            // قاعدة بيانات خادم عادية
            if (settings.UseWindowsAuth)
            {
                return $"Server={settings.ServerName};Database={settings.DatabaseName};Trusted_Connection=true;TrustServerCertificate=true;";
            }
            else
            {
                return $"Server={settings.ServerName};Database={settings.DatabaseName};User Id={settings.Username};Password={settings.Password};TrustServerCertificate=true;";
            }
        }

        /// <summary>
        /// فئة إعدادات قاعدة البيانات
        /// </summary>
        public class DatabaseSettings
        {
            public string ServerName { get; set; } = "localhost";
            public string DatabaseName { get; set; } = "SFDSYS";
            public bool UseWindowsAuth { get; set; } = true;
            public string Username { get; set; } = "sa";
            public string Password { get; set; } = "";
            public bool UseLocalDatabase { get; set; } = false; // قاعدة بيانات محلية في مجلد Data
            public string LocalDatabasePath { get; set; } = ""; // مسار ملف قاعدة البيانات المحلية
        }

        /// <summary>
        /// تحميل الإعدادات من الملف
        /// </summary>
        public static DatabaseSettings LoadSettings()
        {
            try
            {
                // إنشاء مجلد Data إذا لم يكن موجوداً
                var dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                if (!Directory.Exists(dataFolder))
                {
                    Directory.CreateDirectory(dataFolder);
                }

                if (File.Exists(ConfigFilePath))
                {
                    var lines = File.ReadAllLines(ConfigFilePath);
                    var settings = new DatabaseSettings();

                    foreach (var line in lines)
                    {
                        var parts = line.Split('=');
                        if (parts.Length == 2)
                        {
                            var key = parts[0].Trim();
                            var value = parts[1].Trim();

                            switch (key)
                            {
                                case "ServerName":
                                    settings.ServerName = value;
                                    break;
                                case "DatabaseName":
                                    settings.DatabaseName = value;
                                    break;
                                case "UseWindowsAuth":
                                    settings.UseWindowsAuth = bool.Parse(value);
                                    break;
                                case "Username":
                                    settings.Username = value;
                                    break;
                                case "Password":
                                    settings.Password = value;
                                    break;
                                case "UseLocalDatabase":
                                    settings.UseLocalDatabase = bool.Parse(value);
                                    break;
                                case "LocalDatabasePath":
                                    settings.LocalDatabasePath = value;
                                    break;
                            }
                        }
                    }

                    return settings;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل إعدادات قاعدة البيانات: {ex.Message}");
            }

            return new DatabaseSettings();
        }

        /// <summary>
        /// حفظ الإعدادات في الملف
        /// </summary>
        public static void SaveSettings(DatabaseSettings settings)
        {
            try
            {
                // إنشاء مجلد Data إذا لم يكن موجوداً
                var dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                if (!Directory.Exists(dataFolder))
                {
                    Directory.CreateDirectory(dataFolder);
                }

                var lines = new[]
                {
                    $"ServerName={settings.ServerName}",
                    $"DatabaseName={settings.DatabaseName}",
                    $"UseWindowsAuth={settings.UseWindowsAuth}",
                    $"Username={settings.Username}",
                    $"Password={settings.Password}",
                    $"UseLocalDatabase={settings.UseLocalDatabase}",
                    $"LocalDatabasePath={settings.LocalDatabasePath}"
                };

                File.WriteAllLines(ConfigFilePath, lines);
                System.Diagnostics.Debug.WriteLine($"✅ تم حفظ إعدادات قاعدة البيانات في: {ConfigFilePath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ إعدادات قاعدة البيانات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// التحقق من وجود ملف الإعدادات
        /// </summary>
        public static bool HasConfigFile()
        {
            return File.Exists(ConfigFilePath);
        }

        /// <summary>
        /// بناء connection string من الإعدادات
        /// </summary>
        public static string BuildConnectionString(DatabaseSettings settings)
        {
            // إذا كانت قاعدة البيانات محلية
            if (settings.UseLocalDatabase)
            {
                var dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                var dbPath = Path.Combine(dataFolder, $"{settings.DatabaseName}.mdf");

                return $@"Server=(LocalDB)\MSSQLLocalDB;AttachDbFilename={dbPath};Database={settings.DatabaseName};Trusted_Connection=True;";
            }

            // قاعدة بيانات خادم عادية
            if (settings.UseWindowsAuth)
            {
                return $"Server={settings.ServerName};Database={settings.DatabaseName};Trusted_Connection=true;TrustServerCertificate=true;";
            }
            else
            {
                return $"Server={settings.ServerName};Database={settings.DatabaseName};User Id={settings.Username};Password={settings.Password};TrustServerCertificate=true;";
            }
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        public static async Task<bool> TestConnectionAsync(string connectionString)
        {
            try
            {
                using var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString);
                await connection.OpenAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إنشاء قاعدة البيانات إذا لم تكن موجودة
        /// </summary>
        public static async Task<bool> CreateDatabaseIfNotExists()
        {
            try
            {
                var settings = LoadSettings();

                // إذا كانت قاعدة البيانات محلية
                if (settings.UseLocalDatabase)
                {
                    return await CreateLocalDatabaseIfNotExists(settings);
                }

                // التحقق من وجود قاعدة البيانات أولاً
                if (await DatabaseExists(settings))
                {
                    System.Diagnostics.Debug.WriteLine($"✅ قاعدة البيانات موجودة بالفعل: {settings.DatabaseName}");
                    return true;
                }

                // قاعدة بيانات خادم عادية
                var masterConnectionString = BuildConnectionString(new DatabaseSettings
                {
                    ServerName = settings.ServerName,
                    DatabaseName = "master",
                    UseWindowsAuth = settings.UseWindowsAuth,
                    Username = settings.Username,
                    Password = settings.Password,
                    UseLocalDatabase = false
                });

                using var connection = new Microsoft.Data.SqlClient.SqlConnection(masterConnectionString);
                await connection.OpenAsync();

                var createDbCommand = new Microsoft.Data.SqlClient.SqlCommand(
                    $"CREATE DATABASE [{settings.DatabaseName}]", connection);
                await createDbCommand.ExecuteNonQueryAsync();
                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء قاعدة البيانات: {settings.DatabaseName}");

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود قاعدة البيانات
        /// </summary>
        public static async Task<bool> DatabaseExists(DatabaseSettings settings = null)
        {
            try
            {
                if (settings == null)
                    settings = LoadSettings();

                // إذا كانت قاعدة البيانات محلية
                if (settings.UseLocalDatabase)
                {
                    var dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                    var dbPath = Path.Combine(dataFolder, $"{settings.DatabaseName}.mdf");
                    return File.Exists(dbPath);
                }

                // قاعدة بيانات خادم عادية
                var masterConnectionString = BuildConnectionString(new DatabaseSettings
                {
                    ServerName = settings.ServerName,
                    DatabaseName = "master",
                    UseWindowsAuth = settings.UseWindowsAuth,
                    Username = settings.Username,
                    Password = settings.Password,
                    UseLocalDatabase = false
                });

                using var connection = new Microsoft.Data.SqlClient.SqlConnection(masterConnectionString);
                await connection.OpenAsync();

                var checkDbCommand = new Microsoft.Data.SqlClient.SqlCommand(
                    $"SELECT COUNT(*) FROM sys.databases WHERE name = '{settings.DatabaseName}'", connection);
                var dbExists = (int)await checkDbCommand.ExecuteScalarAsync() > 0;

                return dbExists;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص وجود قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء قاعدة البيانات المحلية
        /// </summary>
        public static async Task<bool> CreateLocalDatabaseIfNotExists(DatabaseSettings settings)
        {
            try
            {
                var dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                var dbPath = Path.Combine(dataFolder, $"{settings.DatabaseName}.mdf");
                var logPath = Path.Combine(dataFolder, $"{settings.DatabaseName}_Log.ldf");

                // إنشاء مجلد Data إذا لم يكن موجوداً
                if (!Directory.Exists(dataFolder))
                {
                    Directory.CreateDirectory(dataFolder);
                }

                // إذا كان ملف قاعدة البيانات موجود، لا نحتاج لإنشائه
                if (File.Exists(dbPath))
                {
                    System.Diagnostics.Debug.WriteLine($"✅ قاعدة البيانات المحلية موجودة: {dbPath}");
                    return true;
                }

                // التحقق من وجود قاعدة البيانات في LocalDB أولاً
                var masterConnectionString = @"Server=(LocalDB)\MSSQLLocalDB;Database=master;Trusted_Connection=True;";

                using var connection = new Microsoft.Data.SqlClient.SqlConnection(masterConnectionString);
                await connection.OpenAsync();

                // فحص وجود قاعدة البيانات في LocalDB
                var checkDbCommand = new Microsoft.Data.SqlClient.SqlCommand(
                    $"SELECT COUNT(*) FROM sys.databases WHERE name = '{settings.DatabaseName}'", connection);
                var dbExists = (int)await checkDbCommand.ExecuteScalarAsync() > 0;

                if (dbExists)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ قاعدة البيانات موجودة في LocalDB: {settings.DatabaseName}");
                    return true;
                }

                // إنشاء قاعدة البيانات المحلية
                var createDbCommand = new Microsoft.Data.SqlClient.SqlCommand($@"
                    CREATE DATABASE [{settings.DatabaseName}]
                    ON (NAME = '{settings.DatabaseName}', FILENAME = '{dbPath}')
                    LOG ON (NAME = '{settings.DatabaseName}_Log', FILENAME = '{logPath}')
                ", connection);

                await createDbCommand.ExecuteNonQueryAsync();
                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء قاعدة البيانات المحلية: {dbPath}");

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء قاعدة البيانات المحلية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من إمكانية الاتصال بقاعدة البيانات
        /// </summary>
        public static async Task<bool> CanConnectToDatabase()
        {
            try
            {
                var connectionString = GetConnectionString();
                using var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString);
                await connection.OpenAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ لا يمكن الاتصال بقاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إعداد قاعدة البيانات بشكل آمن
        /// </summary>
        public static async Task<bool> SafeSetupDatabase()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔧 بدء إعداد قاعدة البيانات الآمن...");

                // التحقق من وجود قاعدة البيانات
                var exists = await DatabaseExists();
                System.Diagnostics.Debug.WriteLine($"📊 حالة قاعدة البيانات: {(exists ? "موجودة" : "غير موجودة")}");

                if (!exists)
                {
                    // إنشاء قاعدة البيانات
                    var created = await CreateDatabaseIfNotExists();
                    if (!created)
                    {
                        System.Diagnostics.Debug.WriteLine("❌ فشل في إنشاء قاعدة البيانات");
                        return false;
                    }
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قاعدة البيانات بنجاح");
                }

                // التحقق من إمكانية الاتصال
                var canConnect = await CanConnectToDatabase();
                if (!canConnect)
                {
                    System.Diagnostics.Debug.WriteLine("❌ لا يمكن الاتصال بقاعدة البيانات");
                    return false;
                }

                System.Diagnostics.Debug.WriteLine("✅ تم إعداد قاعدة البيانات بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعداد قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        public static async Task<bool> TestConnectionAsync()
        {
            try
            {
                var connectionString = GetConnectionString();
                using var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString);
                await connection.OpenAsync();
                System.Diagnostics.Debug.WriteLine("✅ تم اختبار الاتصال بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل اختبار الاتصال: {ex.Message}");
                return false;
            }
        }


    }
}
