using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using Prism.Mvvm;

namespace DriverManagementSystem.Models
{
    public class FieldVisit : BindableBase
    {
        public bool _isSelected = false; // Made public for ViewModel access
        public int Id { get; set; }
        public string VisitNumber { get; set; } = string.Empty; // رقم الزيارة - لا يتكرر
        public string DriverContract { get; set; } = string.Empty; // عقد السائق - فريد ومولد آلياً (مثال: 2025061)
        public DateTime AddDate { get; set; } = DateTime.Now; // تاريخ الاضافة
        public string HijriDate { get; set; } = string.Empty; // التاريخ الهجري
        public DateTime DepartureDate { get; set; } // تاريخ النزول
        public DateTime ReturnDate { get; set; } // تاريخ العودة
        public int DaysCount { get; set; } // عدد الايام - آلي
        public string MissionPurpose { get; set; } = string.Empty; // مهمة النزول
        public int SectorId { get; set; } // القطاع
        public string SectorName { get; set; } = string.Empty;
        public int VisitorsCount { get; set; } // عدد القائمين بالزيارة
        public List<FieldVisitor> Visitors { get; set; } = new List<FieldVisitor>(); // القائمين بالزيارة
        // خط السير - مبسط للتوافق
        [NotMapped]
        public List<string> Itinerary { get; set; } = new List<string>();
        public int ProjectsCount { get; set; } // عدد المشاريع
        public List<FieldVisitProject> Projects { get; set; } = new List<FieldVisitProject>(); // المشاريع

        // خصائص محسوبة للعرض في DataGrid
        [NotMapped]
        public List<string> ProjectNames => Projects?.Select(p => $"{p.ProjectNumber} - {p.ProjectName}").ToList() ?? new List<string>();

        [NotMapped]
        public List<string> VisitorNames => Visitors?.Select(v => $"{v.OfficerRank} {v.OfficerName}").ToList() ?? new List<string>();

        [NotMapped]
        public string ProjectsDisplay => Projects?.Any() == true ? string.Join(", ", Projects.Select(p => p.ProjectNumber)) : "لا توجد مشاريع";

        [NotMapped]
        public string VisitorsDisplay => Visitors?.Any() == true ? string.Join(", ", Visitors.Select(v => v.OfficerName)) : "لا يوجد قائمين";

        /// <summary>
        /// مجموع أيام المشاريع المحددة
        /// </summary>
        [NotMapped]
        public int TotalProjectDays => Projects?.Sum(p => p.ProjectDays) ?? 0;

        public string SelectedDrivers { get; set; } = string.Empty; // السائقين المحددين مع عروضهم
        public string WinnerDriverMessage { get; set; } = string.Empty; // نص الرسالة للسائق الفائز
        public string SecurityRoute { get; set; } = string.Empty; // خط السير للنقاط الأمنية
        public string VisitNotes { get; set; } = string.Empty; // ملاحظات إضافية

        // الحقول الجديدة من Excel
        public string ApprovalBy { get; set; } = string.Empty; // الموافقة على السفر (مخفي)
        public DateTime? SubmissionTime { get; set; } // وقت وتاريخ الإرسال
        public string OdkVisitNumber { get; set; } = string.Empty; // رقم الزيارة ODK

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public bool IsActive { get; set; } = true;
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (SetProperty(ref _isSelected, value))
                {
                    // Handle single selection logic here
                    OnSelectionChanged();
                }
            }
        }

        // Reference to parent collection for single selection
        public static event Action<FieldVisit>? SelectionChanged;

        private void OnSelectionChanged()
        {
            if (_isSelected)
            {
                SelectionChanged?.Invoke(this);
            }
        }

        public void NotifyPropertyChanged(string propertyName)
        {
            RaisePropertyChanged(propertyName);
        }
    }

    public class FieldVisitor
    {
        public int Id { get; set; }
        public int FieldVisitId { get; set; }
        public string Name { get; set; } = string.Empty; // اسم القائم بالزيارة
        public int OfficerId { get; set; } // المرتبط بالقطاع
        public string OfficerName { get; set; } = string.Empty;
        public string OfficerRank { get; set; } = string.Empty;
        public string OfficerCode { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty; // رقم الهاتف
    }
}
