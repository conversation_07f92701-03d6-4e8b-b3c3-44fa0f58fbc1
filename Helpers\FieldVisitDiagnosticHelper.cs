using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Helpers
{
    /// <summary>
    /// أداة تشخيص مشاكل تحديث الزيارات الميدانية
    /// </summary>
    public class FieldVisitDiagnosticHelper
    {
        private readonly DatabaseService _dataService;

        public FieldVisitDiagnosticHelper(DatabaseService dataService)
        {
            _dataService = dataService;
        }

        /// <summary>
        /// تشخيص شامل لمشكلة تحديث الزيارة
        /// </summary>
        public async Task<DiagnosticResult> DiagnoseFieldVisitUpdateAsync(FieldVisit visit)
        {
            var result = new DiagnosticResult();
            
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 بدء تشخيص الزيارة: {visit.VisitNumber} (ID: {visit.Id})");

                // 1. فحص البيانات الأساسية
                await CheckBasicDataAsync(visit, result);

                // 2. فحص قاعدة البيانات
                await CheckDatabaseConnectionAsync(result);

                // 3. فحص وجود الزيارة في قاعدة البيانات
                await CheckVisitExistsAsync(visit, result);

                // 4. فحص صحة القطاع
                await CheckSectorValidityAsync(visit, result);

                // 5. فحص صحة الزوار
                await CheckVisitorsValidityAsync(visit, result);

                // 6. فحص صحة المشاريع
                await CheckProjectsValidityAsync(visit, result);

                // 7. محاولة تحديث تجريبية
                await TestUpdateAsync(visit, result);

                result.IsSuccessful = !result.Errors.Any();
                
                System.Diagnostics.Debug.WriteLine($"✅ انتهى التشخيص - النتيجة: {(result.IsSuccessful ? "نجح" : "فشل")}");
                
                return result;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في التشخيص: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التشخيص: {ex.Message}");
                return result;
            }
        }

        private async Task CheckBasicDataAsync(FieldVisit visit, DiagnosticResult result)
        {
            result.Steps.Add("1️⃣ فحص البيانات الأساسية");

            if (string.IsNullOrWhiteSpace(visit.VisitNumber))
            {
                result.Errors.Add("رقم الزيارة فارغ");
            }
            else
            {
                result.Info.Add($"رقم الزيارة: {visit.VisitNumber}");
            }

            if (visit.Id <= 0)
            {
                result.Errors.Add("معرف الزيارة غير صحيح");
            }
            else
            {
                result.Info.Add($"معرف الزيارة: {visit.Id}");
            }

            if (visit.SectorId <= 0)
            {
                result.Errors.Add("معرف القطاع غير صحيح");
            }
            else
            {
                result.Info.Add($"معرف القطاع: {visit.SectorId}");
            }

            if (string.IsNullOrWhiteSpace(visit.MissionPurpose))
            {
                result.Errors.Add("مهمة النزول فارغة");
            }

            result.Info.Add($"عدد الزوار: {visit.VisitorsCount}");
            result.Info.Add($"عدد المشاريع: {visit.ProjectsCount}");
        }

        private async Task CheckDatabaseConnectionAsync(DiagnosticResult result)
        {
            result.Steps.Add("2️⃣ فحص اتصال قاعدة البيانات");

            try
            {
                var visits = await _dataService.GetFieldVisitsAsync();
                result.Info.Add($"✅ الاتصال بقاعدة البيانات ناجح - عدد الزيارات: {visits.Count}");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"فشل الاتصال بقاعدة البيانات: {ex.Message}");
            }
        }

        private async Task CheckVisitExistsAsync(FieldVisit visit, DiagnosticResult result)
        {
            result.Steps.Add("3️⃣ فحص وجود الزيارة في قاعدة البيانات");

            try
            {
                var visits = await _dataService.GetFieldVisitsAsync();
                var existingVisit = visits.FirstOrDefault(v => v.Id == visit.Id);

                if (existingVisit == null)
                {
                    result.Errors.Add($"الزيارة بالمعرف {visit.Id} غير موجودة في قاعدة البيانات");
                }
                else
                {
                    result.Info.Add($"✅ الزيارة موجودة في قاعدة البيانات");
                    result.Info.Add($"رقم الزيارة في قاعدة البيانات: {existingVisit.VisitNumber}");
                    result.Info.Add($"القطاع في قاعدة البيانات: {existingVisit.SectorName}");
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في فحص وجود الزيارة: {ex.Message}");
            }
        }

        private async Task CheckSectorValidityAsync(FieldVisit visit, DiagnosticResult result)
        {
            result.Steps.Add("4️⃣ فحص صحة القطاع");

            try
            {
                var sectors = await _dataService.GetSectorsAsync();
                var sector = sectors.FirstOrDefault(s => s.Id == visit.SectorId);

                if (sector == null)
                {
                    result.Errors.Add($"القطاع بالمعرف {visit.SectorId} غير موجود");
                }
                else
                {
                    result.Info.Add($"✅ القطاع صحيح: {sector.Name}");
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في فحص القطاع: {ex.Message}");
            }
        }

        private async Task CheckVisitorsValidityAsync(FieldVisit visit, DiagnosticResult result)
        {
            result.Steps.Add("5️⃣ فحص صحة الزوار");

            try
            {
                if (visit.Visitors?.Any() == true)
                {
                    var officers = await _dataService.GetOfficersAsync();
                    
                    foreach (var visitor in visit.Visitors)
                    {
                        if (visitor.OfficerId > 0)
                        {
                            var officer = officers.FirstOrDefault(o => o.Id == visitor.OfficerId);
                            if (officer == null)
                            {
                                result.Warnings.Add($"الضابط بالمعرف {visitor.OfficerId} غير موجود");
                            }
                        }
                    }
                    
                    result.Info.Add($"✅ تم فحص {visit.Visitors.Count} زائر");
                }
                else
                {
                    result.Info.Add("لا توجد زوار للفحص");
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في فحص الزوار: {ex.Message}");
            }
        }

        private async Task CheckProjectsValidityAsync(FieldVisit visit, DiagnosticResult result)
        {
            result.Steps.Add("6️⃣ فحص صحة المشاريع");

            try
            {
                if (visit.Projects?.Any() == true)
                {
                    var projects = await _dataService.GetProjectsAsync();
                    
                    foreach (var visitProject in visit.Projects)
                    {
                        if (visitProject.ProjectId.HasValue)
                        {
                            var project = projects.FirstOrDefault(p => p.Id == visitProject.ProjectId.Value);
                            if (project == null)
                            {
                                result.Warnings.Add($"المشروع بالمعرف {visitProject.ProjectId} غير موجود");
                            }
                        }
                    }
                    
                    result.Info.Add($"✅ تم فحص {visit.Projects.Count} مشروع");
                }
                else
                {
                    result.Info.Add("لا توجد مشاريع للفحص");
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في فحص المشاريع: {ex.Message}");
            }
        }

        private async Task TestUpdateAsync(FieldVisit visit, DiagnosticResult result)
        {
            result.Steps.Add("7️⃣ اختبار التحديث");

            try
            {
                // عمل نسخة من الزيارة للاختبار
                var testVisit = new FieldVisit
                {
                    Id = visit.Id,
                    VisitNumber = visit.VisitNumber,
                    AddDate = visit.AddDate,
                    HijriDate = visit.HijriDate,
                    DepartureDate = visit.DepartureDate,
                    ReturnDate = visit.ReturnDate,
                    DaysCount = visit.DaysCount,
                    MissionPurpose = visit.MissionPurpose + " - اختبار تشخيصي",
                    SectorId = visit.SectorId,
                    SectorName = visit.SectorName,
                    VisitorsCount = visit.VisitorsCount,
                    Visitors = visit.Visitors?.ToList() ?? new List<FieldVisitor>(),
                    Itinerary = visit.Itinerary?.ToList() ?? new List<string>(),
                    Projects = visit.Projects?.ToList() ?? new List<FieldVisitProject>(),
                    ProjectsCount = visit.ProjectsCount,
                    SecurityRoute = visit.SecurityRoute,
                    VisitNotes = visit.VisitNotes
                };

                var success = await _dataService.UpdateFieldVisitAsync(testVisit);
                
                if (success)
                {
                    result.Info.Add("✅ اختبار التحديث نجح");
                    
                    // إعادة البيانات الأصلية
                    visit.MissionPurpose = visit.MissionPurpose.Replace(" - اختبار تشخيصي", "");
                    await _dataService.UpdateFieldVisitAsync(visit);
                }
                else
                {
                    result.Errors.Add("❌ اختبار التحديث فشل");
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في اختبار التحديث: {ex.Message}");
            }
        }

        /// <summary>
        /// عرض نتائج التشخيص في رسالة
        /// </summary>
        public static void ShowDiagnosticResults(DiagnosticResult result)
        {
            var message = "🔍 نتائج تشخيص مشكلة تحديث الزيارة:\n\n";

            // الخطوات المنفذة
            message += "📋 الخطوات المنفذة:\n";
            foreach (var step in result.Steps)
            {
                message += $"• {step}\n";
            }

            // الأخطاء
            if (result.Errors.Any())
            {
                message += "\n❌ الأخطاء المكتشفة:\n";
                foreach (var error in result.Errors)
                {
                    message += $"• {error}\n";
                }
            }

            // التحذيرات
            if (result.Warnings.Any())
            {
                message += "\n⚠️ التحذيرات:\n";
                foreach (var warning in result.Warnings)
                {
                    message += $"• {warning}\n";
                }
            }

            // المعلومات
            if (result.Info.Any())
            {
                message += "\n📊 معلومات إضافية:\n";
                foreach (var info in result.Info)
                {
                    message += $"• {info}\n";
                }
            }

            // النتيجة النهائية
            message += $"\n🎯 النتيجة: {(result.IsSuccessful ? "✅ لا توجد مشاكل" : "❌ توجد مشاكل تحتاج إصلاح")}";

            MessageBox.Show(message, "نتائج التشخيص", MessageBoxButton.OK, 
                result.IsSuccessful ? MessageBoxImage.Information : MessageBoxImage.Warning);
        }
    }

    /// <summary>
    /// نتيجة التشخيص
    /// </summary>
    public class DiagnosticResult
    {
        public bool IsSuccessful { get; set; }
        public List<string> Steps { get; set; } = new List<string>();
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public List<string> Info { get; set; } = new List<string>();
    }
}
