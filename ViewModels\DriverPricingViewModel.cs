using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Prism.Commands;
using Prism.Mvvm;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel احترافي لإدارة أسعار السائقين
    /// </summary>
    public class DriverPricingViewModel : BindableBase
    {
        private readonly IDataService _dataService;
        private ObservableCollection<Driver> _drivers = new();
        private ObservableCollection<DriverQuote> _driverQuotes = new();
        private Driver _selectedDriver;
        private DriverQuote _selectedQuote;
        private decimal _newPrice;
        private int _newDays = 1;
        private string _newNotes = string.Empty;
        private bool _isLoading;
        private string _searchText = string.Empty;

        public DriverPricingViewModel(IDataService dataService)
        {
            _dataService = dataService;
            
            // Initialize commands
            LoadDataCommand = new DelegateCommand(async () => await LoadDataAsync());
            AddQuoteCommand = new DelegateCommand(async () => await AddQuoteAsync(), CanAddQuote);
            UpdateQuoteCommand = new DelegateCommand(async () => await UpdateQuoteAsync(), CanUpdateQuote);
            DeleteQuoteCommand = new DelegateCommand(async () => await DeleteQuoteAsync(), CanDeleteQuote);
            AcceptQuoteCommand = new DelegateCommand(async () => await AcceptQuoteAsync(), CanAcceptQuote);
            RejectQuoteCommand = new DelegateCommand(async () => await RejectQuoteAsync(), CanRejectQuote);
            ClearFormCommand = new DelegateCommand(ClearForm);
            RefreshCommand = new DelegateCommand(async () => await LoadDataAsync());

            // Load initial data
            _ = LoadDataAsync();
        }

        #region Properties

        public ObservableCollection<Driver> Drivers
        {
            get => _drivers;
            set => SetProperty(ref _drivers, value);
        }

        public ObservableCollection<DriverQuote> DriverQuotes
        {
            get => _driverQuotes;
            set => SetProperty(ref _driverQuotes, value);
        }

        public Driver SelectedDriver
        {
            get => _selectedDriver;
            set
            {
                if (SetProperty(ref _selectedDriver, value))
                {
                    OnDriverSelected();
                    RaiseCanExecuteChanged();
                }
            }
        }

        public DriverQuote SelectedQuote
        {
            get => _selectedQuote;
            set
            {
                if (SetProperty(ref _selectedQuote, value))
                {
                    OnQuoteSelected();
                    RaiseCanExecuteChanged();
                }
            }
        }

        public decimal NewPrice
        {
            get => _newPrice;
            set
            {
                if (SetProperty(ref _newPrice, value))
                {
                    RaiseCanExecuteChanged();
                }
            }
        }

        public int NewDays
        {
            get => _newDays;
            set
            {
                if (SetProperty(ref _newDays, value))
                {
                    RaiseCanExecuteChanged();
                }
            }
        }

        public string NewNotes
        {
            get => _newNotes;
            set => SetProperty(ref _newNotes, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        // Statistics Properties
        public int TotalQuotes => DriverQuotes.Count;
        public int PendingQuotes => DriverQuotes.Count(q => q.Status == QuoteStatus.Pending);
        public int AcceptedQuotes => DriverQuotes.Count(q => q.Status == QuoteStatus.Accepted);
        public int RejectedQuotes => DriverQuotes.Count(q => q.Status == QuoteStatus.Rejected);
        public decimal AveragePrice => DriverQuotes.Any() ? DriverQuotes.Average(q => q.QuotedPrice) : 0;
        public decimal LowestPrice => DriverQuotes.Any() ? DriverQuotes.Min(q => q.QuotedPrice) : 0;
        public decimal HighestPrice => DriverQuotes.Any() ? DriverQuotes.Max(q => q.QuotedPrice) : 0;

        #endregion

        #region Commands

        public ICommand LoadDataCommand { get; }
        public ICommand AddQuoteCommand { get; }
        public ICommand UpdateQuoteCommand { get; }
        public ICommand DeleteQuoteCommand { get; }
        public ICommand AcceptQuoteCommand { get; }
        public ICommand RejectQuoteCommand { get; }
        public ICommand ClearFormCommand { get; }
        public ICommand RefreshCommand { get; }

        #endregion

        #region Methods

        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;

                // Load drivers and quotes
                var driversTask = _dataService.GetDriversAsync();
                var quotesTask = _dataService.GetDriverQuotesAsync();

                await Task.WhenAll(driversTask, quotesTask);

                Drivers = new ObservableCollection<Driver>(driversTask.Result);
                DriverQuotes = new ObservableCollection<DriverQuote>(quotesTask.Result);
                
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void OnDriverSelected()
        {
            if (SelectedDriver != null)
            {
                // Auto-fill form with driver info
                NewPrice = SelectedDriver.QuotedPrice ?? 0;
                NewDays = SelectedDriver.QuotedDays ?? 1;
                NewNotes = SelectedDriver.QuoteNotes ?? string.Empty;
            }
        }

        private void OnQuoteSelected()
        {
            if (SelectedQuote != null)
            {
                // Fill form with selected quote data
                NewPrice = SelectedQuote.QuotedPrice;
                NewDays = SelectedQuote.QuotedDays;
                NewNotes = SelectedQuote.Notes;
                
                // Find and select the corresponding driver
                SelectedDriver = Drivers.FirstOrDefault(d => d.Id == SelectedQuote.DriverId);
            }
        }

        private async Task AddQuoteAsync()
        {
            if (SelectedDriver == null) return;

            try
            {
                var quote = new DriverQuote
                {
                    DriverId = SelectedDriver.Id,
                    DriverName = SelectedDriver.Name,
                    DriverCode = SelectedDriver.DriverCode,
                    PhoneNumber = SelectedDriver.PhoneNumber,
                    VehicleType = SelectedDriver.VehicleType,
                    VehicleNumber = SelectedDriver.VehicleNumber,
                    QuotedPrice = NewPrice,
                    QuotedDays = NewDays,
                    Notes = NewNotes,
                    Status = QuoteStatus.Pending
                };

                var success = await _dataService.AddDriverQuoteAsync(quote);
                if (success)
                {
                    await LoadDataAsync();
                    ClearForm();
                    MessageBox.Show("تم إضافة عرض السعر بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("فشل في إضافة عرض السعر", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة عرض السعر: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task UpdateQuoteAsync()
        {
            if (SelectedQuote == null) return;

            try
            {
                SelectedQuote.QuotedPrice = NewPrice;
                SelectedQuote.QuotedDays = NewDays;
                SelectedQuote.Notes = NewNotes;

                var success = await _dataService.UpdateDriverQuoteAsync(SelectedQuote);
                if (success)
                {
                    await LoadDataAsync();
                    MessageBox.Show("تم تحديث عرض السعر بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("فشل في تحديث عرض السعر", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث عرض السعر: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task DeleteQuoteAsync()
        {
            if (SelectedQuote == null) return;

            var result = MessageBox.Show($"هل أنت متأكد من حذف عرض السعر للسائق {SelectedQuote.DriverName}؟", 
                "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    var success = await _dataService.DeleteDriverQuoteAsync(SelectedQuote.Id);
                    if (success)
                    {
                        await LoadDataAsync();
                        ClearForm();
                        MessageBox.Show("تم حذف عرض السعر بنجاح", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف عرض السعر", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف عرض السعر: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async Task AcceptQuoteAsync()
        {
            if (SelectedQuote == null) return;

            try
            {
                var success = await _dataService.UpdateDriverQuoteStatusAsync(SelectedQuote.Id, QuoteStatus.Accepted);
                if (success)
                {
                    await LoadDataAsync();
                    MessageBox.Show($"تم قبول عرض السائق {SelectedQuote.DriverName}", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في قبول العرض: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task RejectQuoteAsync()
        {
            if (SelectedQuote == null) return;

            try
            {
                var success = await _dataService.UpdateDriverQuoteStatusAsync(SelectedQuote.Id, QuoteStatus.Rejected);
                if (success)
                {
                    await LoadDataAsync();
                    MessageBox.Show($"تم رفض عرض السائق {SelectedQuote.DriverName}", "تم", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في رفض العرض: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearForm()
        {
            SelectedDriver = null;
            SelectedQuote = null;
            NewPrice = 0;
            NewDays = 1;
            NewNotes = string.Empty;
        }

        private void UpdateStatistics()
        {
            RaisePropertyChanged(nameof(TotalQuotes));
            RaisePropertyChanged(nameof(PendingQuotes));
            RaisePropertyChanged(nameof(AcceptedQuotes));
            RaisePropertyChanged(nameof(RejectedQuotes));
            RaisePropertyChanged(nameof(AveragePrice));
            RaisePropertyChanged(nameof(LowestPrice));
            RaisePropertyChanged(nameof(HighestPrice));
        }

        private void RaiseCanExecuteChanged()
        {
            ((DelegateCommand)AddQuoteCommand).RaiseCanExecuteChanged();
            ((DelegateCommand)UpdateQuoteCommand).RaiseCanExecuteChanged();
            ((DelegateCommand)DeleteQuoteCommand).RaiseCanExecuteChanged();
            ((DelegateCommand)AcceptQuoteCommand).RaiseCanExecuteChanged();
            ((DelegateCommand)RejectQuoteCommand).RaiseCanExecuteChanged();
        }

        #endregion

        #region Command Can Execute Methods

        private bool CanAddQuote() => SelectedDriver != null && NewPrice > 0 && NewDays > 0;
        private bool CanUpdateQuote() => SelectedQuote != null && NewPrice > 0 && NewDays > 0;
        private bool CanDeleteQuote() => SelectedQuote != null;
        private bool CanAcceptQuote() => SelectedQuote != null && SelectedQuote.Status != QuoteStatus.Accepted;
        private bool CanRejectQuote() => SelectedQuote != null && SelectedQuote.Status != QuoteStatus.Rejected;

        #endregion
    }
}
