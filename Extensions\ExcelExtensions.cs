using System;
using ClosedXML.Excel;

namespace DriverManagementSystem.Extensions
{
    /// <summary>
    /// Extension methods لتسهيل التعامل مع ClosedXML
    /// </summary>
    public static class ExcelExtensions
    {
        /// <summary>
        /// الحصول على خلية من صف بناءً على اسم العمود
        /// </summary>
        public static IXLCell Field(this IXLRangeRow row, string columnName)
        {
            try
            {
                var table = row.Worksheet.RangeUsed().AsTable();
                var headerRow = table.HeadersRow();
                
                // البحث عن العمود بالاسم
                for (int col = 1; col <= headerRow.CellCount(); col++)
                {
                    var headerCell = headerRow.Cell(col);
                    if (headerCell.GetString().Trim().Equals(columnName, StringComparison.OrdinalIgnoreCase))
                    {
                        return row.Cell(col);
                    }
                }
                
                // إذا لم يتم العثور على العمود، إرجاع خلية فارغة
                return row.Worksheet.Cell(1, 1).Clear();
            }
            catch
            {
                // في حالة الخطأ، إرجاع خلية فارغة
                return row.Worksheet.Cell(1, 1).Clear();
            }
        }

        /// <summary>
        /// الحصول على النص من الخلية مع معالجة القيم الفارغة
        /// </summary>
        public static string GetString(this IXLCell cell)
        {
            try
            {
                if (cell == null || cell.IsEmpty())
                    return string.Empty;
                
                return cell.GetValue<string>() ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// الحصول على التاريخ من الخلية مع معالجة القيم الفارغة
        /// </summary>
        public static DateTime? GetDateTime(this IXLCell cell)
        {
            try
            {
                if (cell == null || cell.IsEmpty())
                    return null;

                // محاولة الحصول على التاريخ مباشرة
                if (cell.TryGetValue(out DateTime dateValue))
                    return dateValue;

                // محاولة تحويل النص إلى تاريخ
                var cellText = cell.GetString();
                if (string.IsNullOrWhiteSpace(cellText))
                    return null;

                // محاولة تحويل صيغ مختلفة من التاريخ
                if (DateTime.TryParse(cellText, out DateTime parsedDate))
                    return parsedDate;

                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// الحصول على قيمة من نوع محدد مع معالجة الأخطاء
        /// </summary>
        public static T GetValue<T>(this IXLCell cell)
        {
            try
            {
                if (cell == null || cell.IsEmpty())
                    return default(T);

                return cell.GetValue<T>();
            }
            catch
            {
                return default(T);
            }
        }
    }
}
