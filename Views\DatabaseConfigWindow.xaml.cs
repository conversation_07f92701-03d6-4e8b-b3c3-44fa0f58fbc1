using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.Views
{
    public partial class DatabaseConfigWindow : Window
    {
        public bool ConfigurationSaved { get; private set; } = false;

        public DatabaseConfigWindow()
        {
            InitializeComponent();
            LoadCurrentSettings();
            UpdateLocalDatabasePath();
            LoadAvailableDatabases();
        }

        /// <summary>
        /// تحميل الإعدادات الحالية
        /// </summary>
        private void LoadCurrentSettings()
        {
            try
            {
                var settings = DatabaseConfig.LoadSettings();

                TxtServerName.Text = settings.ServerName;
                TxtDatabaseName.Text = settings.DatabaseName;
                TxtLocalDatabaseName.Text = settings.DatabaseName;
                TxtUsername.Text = settings.Username;
                TxtPassword.Password = settings.Password;

                RadioWindowsAuth.IsChecked = settings.UseWindowsAuth;
                RadioSqlAuth.IsChecked = !settings.UseWindowsAuth;

                RadioServerDatabase.IsChecked = !settings.UseLocalDatabase;
                RadioLocalDatabase.IsChecked = settings.UseLocalDatabase;

                UpdateAuthenticationVisibility();
                UpdateDatabaseTypeVisibility();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث مسار قاعدة البيانات المحلية
        /// </summary>
        private void UpdateLocalDatabasePath()
        {
            try
            {
                var dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                var dbName = string.IsNullOrWhiteSpace(TxtLocalDatabaseName.Text) ? "SFDSYS" : TxtLocalDatabaseName.Text;
                var dbPath = Path.Combine(dataFolder, $"{dbName}.mdf");

                TxtLocalDatabasePath.Text = dataFolder;
                TxtLocalDbFullPath.Text = dbPath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث مسار قاعدة البيانات: {ex.Message}");
            }
        }

        private void RadioWindowsAuth_Checked(object sender, RoutedEventArgs e)
        {
            UpdateAuthenticationVisibility();
        }

        private void RadioSqlAuth_Checked(object sender, RoutedEventArgs e)
        {
            UpdateAuthenticationVisibility();
        }

        private void RadioServerDatabase_Checked(object sender, RoutedEventArgs e)
        {
            UpdateDatabaseTypeVisibility();
        }

        private void RadioLocalDatabase_Checked(object sender, RoutedEventArgs e)
        {
            UpdateDatabaseTypeVisibility();
            UpdateLocalDatabasePath();
        }

        /// <summary>
        /// تحديث رؤية إعدادات المصادقة
        /// </summary>
        private void UpdateAuthenticationVisibility()
        {
            if (SqlAuthPanel != null)
            {
                SqlAuthPanel.Visibility = RadioSqlAuth.IsChecked == true ? Visibility.Visible : Visibility.Collapsed;
            }
        }

        /// <summary>
        /// تحديث رؤية نوع قاعدة البيانات
        /// </summary>
        private void UpdateDatabaseTypeVisibility()
        {
            if (ServerSettingsGroup != null && LocalDatabaseGroup != null)
            {
                if (RadioLocalDatabase.IsChecked == true)
                {
                    ServerSettingsGroup.Visibility = Visibility.Collapsed;
                    LocalDatabaseGroup.Visibility = Visibility.Visible;
                }
                else
                {
                    ServerSettingsGroup.Visibility = Visibility.Visible;
                    LocalDatabaseGroup.Visibility = Visibility.Collapsed;
                }
            }
        }

        /// <summary>
        /// حفظ الإعدادات
        /// </summary>
        private async Task<bool> SaveSettingsAsync()
        {
            try
            {
                BtnSaveSettings.IsEnabled = false;
                BtnSaveSettings.Content = "⏳ جاري الحفظ...";

                var settings = new DatabaseConfig.DatabaseSettings();

                if (RadioLocalDatabase.IsChecked == true)
                {
                    // قاعدة بيانات محلية
                    settings.UseLocalDatabase = true;
                    settings.DatabaseName = TxtLocalDatabaseName.Text.Trim();
                    settings.LocalDatabasePath = TxtLocalDatabasePath.Text.Trim();
                    settings.UseWindowsAuth = true; // LocalDB يستخدم Windows Auth دائماً
                }
                else
                {
                    // قاعدة بيانات خادم
                    settings.UseLocalDatabase = false;
                    settings.ServerName = TxtServerName.Text.Trim();
                    settings.DatabaseName = TxtDatabaseName.Text.Trim();
                    settings.UseWindowsAuth = RadioWindowsAuth.IsChecked == true;
                    settings.Username = TxtUsername.Text.Trim();
                    settings.Password = TxtPassword.Password;
                }

                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(settings.DatabaseName))
                {
                    MessageBox.Show("يرجى إدخال اسم قاعدة البيانات", "خطأ في البيانات",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                if (!settings.UseLocalDatabase)
                {
                    if (string.IsNullOrWhiteSpace(settings.ServerName))
                    {
                        MessageBox.Show("يرجى إدخال اسم الخادم", "خطأ في البيانات",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return false;
                    }

                    if (!settings.UseWindowsAuth && string.IsNullOrWhiteSpace(settings.Username))
                    {
                        MessageBox.Show("يرجى إدخال اسم المستخدم", "خطأ في البيانات",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return false;
                    }
                }

                // حفظ الإعدادات
                DatabaseConfig.SaveSettings(settings);

                ConfigurationSaved = true;

                MessageBox.Show("✅ تم حفظ إعدادات قاعدة البيانات بنجاح!", "نجح الحفظ",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                System.Diagnostics.Debug.WriteLine($"✅ تم حفظ إعدادات قاعدة البيانات: {settings.DatabaseName}");
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ إعدادات قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حفظ الإعدادات - Event Handler
        /// </summary>
        private async void BtnSaveSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                BtnSaveSettings.IsEnabled = false;
                BtnSaveSettings.Content = "⏳ جاري الحفظ...";

                await SaveSettingsAsync();
            }
            finally
            {
                BtnSaveSettings.IsEnabled = true;
                BtnSaveSettings.Content = "💾 حفظ الإعدادات";
            }
        }

        /// <summary>
        /// اختبار الاتصال
        /// </summary>
        private async void BtnTestConnection_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                BtnTestConnection.IsEnabled = false;
                BtnTestConnection.Content = "⏳ جاري الاختبار...";

                // حفظ الإعدادات مؤقتاً للاختبار
                var saved = await SaveSettingsAsync();

                if (!saved)
                {
                    return;
                }

                // التحقق من LocalDB إذا كانت قاعدة البيانات محلية
                var settings = DatabaseConfig.LoadSettings();
                if (settings.UseLocalDatabase)
                {
                    var localDBAvailable = await DatabaseConfig.IsLocalDBAvailable();
                    if (!localDBAvailable)
                    {
                        MessageBox.Show("❌ SQL Server LocalDB غير متوفر!\n\nيرجى تثبيت SQL Server LocalDB أو استخدام قاعدة بيانات خادم.",
                            "LocalDB غير متوفر", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                }

                var connectionString = DatabaseConfig.GetConnectionString();
                var success = await DatabaseConfig.TestConnectionAsync(connectionString);

                if (success)
                {
                    MessageBox.Show("✅ تم الاتصال بقاعدة البيانات بنجاح!", "نجح الاتصال",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    var errorMessage = "❌ فشل الاتصال بقاعدة البيانات!\n\n";

                    if (settings.UseLocalDatabase)
                    {
                        errorMessage += "تحقق من:\n";
                        errorMessage += "• تثبيت SQL Server LocalDB\n";
                        errorMessage += "• صحة اسم قاعدة البيانات\n";
                        errorMessage += "• وجود مجلد Data في مسار التطبيق";
                    }
                    else
                    {
                        errorMessage += "تحقق من:\n";
                        errorMessage += "• اسم الخادم\n";
                        errorMessage += "• اسم قاعدة البيانات\n";
                        errorMessage += "• بيانات المصادقة\n";
                        errorMessage += "• الاتصال بالشبكة";
                    }

                    MessageBox.Show(errorMessage, "فشل الاتصال",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الاتصال: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                BtnTestConnection.IsEnabled = true;
                BtnTestConnection.Content = "🔍 اختبار الاتصال";
            }
        }

        /// <summary>
        /// إنشاء قاعدة البيانات
        /// </summary>
        private async void BtnCreateDatabase_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                BtnCreateDatabase.IsEnabled = false;
                BtnCreateDatabase.Content = "⏳ جاري الإنشاء...";

                // حفظ الإعدادات أولاً
                var saved = await SaveSettingsAsync();

                if (!saved)
                {
                    return;
                }

                var success = await DatabaseConfig.CreateDatabaseIfNotExists();

                if (success)
                {
                    MessageBox.Show("✅ تم إنشاء قاعدة البيانات بنجاح!", "نجح الإنشاء", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("❌ فشل في إنشاء قاعدة البيانات!\nيرجى التحقق من الإعدادات والصلاحيات.", "فشل الإنشاء", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء قاعدة البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                BtnCreateDatabase.IsEnabled = true;
                BtnCreateDatabase.Content = "🏗️ إنشاء قاعدة البيانات";
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void TxtLocalDatabaseName_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            UpdateLocalDatabasePath();
        }

        /// <summary>
        /// تحميل قائمة قواعد البيانات المتاحة
        /// </summary>
        private async void LoadAvailableDatabases()
        {
            try
            {
                var databases = await Data.DatabaseConfig.GetAvailableDatabases();
                ListDatabases.ItemsSource = databases;

                if (databases.Count == 0)
                {
                    ListDatabases.ItemsSource = new List<string> { "لا توجد قواعد بيانات متاحة" };
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قواعد البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث قائمة قواعد البيانات
        /// </summary>
        private async void BtnRefreshDatabases_Click(object sender, RoutedEventArgs e)
        {
            BtnRefreshDatabases.IsEnabled = false;
            BtnRefreshDatabases.Content = "⏳ جاري التحديث...";

            try
            {
                await Task.Delay(500); // تأخير بسيط للمؤثر البصري
                LoadAvailableDatabases();
            }
            finally
            {
                BtnRefreshDatabases.IsEnabled = true;
                BtnRefreshDatabases.Content = "🔄 تحديث القائمة";
            }
        }

        /// <summary>
        /// اختيار قاعدة بيانات من القائمة
        /// </summary>
        private void BtnSelectDatabase_Click(object sender, RoutedEventArgs e)
        {
            if (ListDatabases.SelectedItem is string selectedDatabase &&
                selectedDatabase != "لا توجد قواعد بيانات متاحة")
            {
                // تحديد نوع قاعدة البيانات كمحلية
                RadioLocalDatabase.IsChecked = true;

                // تعيين اسم قاعدة البيانات
                TxtLocalDatabaseName.Text = selectedDatabase;

                MessageBox.Show($"تم اختيار قاعدة البيانات: {selectedDatabase}", "تم الاختيار",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("يرجى اختيار قاعدة بيانات من القائمة أولاً", "لم يتم الاختيار",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// حذف قاعدة بيانات
        /// </summary>
        private async void BtnDeleteDatabase_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string databaseName)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف قاعدة البيانات '{databaseName}'؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه!\n\nسيتم إغلاق جميع الاتصالات النشطة بقاعدة البيانات.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    button.IsEnabled = false;
                    button.Content = "⏳";

                    // تعطيل جميع أزرار الحذف الأخرى مؤقتاً
                    foreach (var item in ListDatabases.Items)
                    {
                        var container = ListDatabases.ItemContainerGenerator.ContainerFromItem(item);
                        if (container != null)
                        {
                            var deleteBtn = FindVisualChild<Button>(container as DependencyObject);
                            if (deleteBtn != null && deleteBtn != button)
                            {
                                deleteBtn.IsEnabled = false;
                            }
                        }
                    }

                    try
                    {
                        var deleted = await Data.DatabaseConfig.DeleteDatabase(databaseName);

                        if (deleted)
                        {
                            MessageBox.Show($"✅ تم حذف قاعدة البيانات '{databaseName}' بنجاح", "تم الحذف",
                                MessageBoxButton.OK, MessageBoxImage.Information);

                            // تحديث القائمة
                            LoadAvailableDatabases();
                        }
                        else
                        {
                            MessageBox.Show($"❌ فشل في حذف قاعدة البيانات '{databaseName}'\n\nالأسباب المحتملة:\n• قاعدة البيانات قيد الاستخدام\n• عدم وجود صلاحيات كافية\n• قاعدة البيانات محمية\n\nحاول إغلاق جميع التطبيقات المتصلة بقاعدة البيانات وأعد المحاولة.",
                                "فشل الحذف", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"❌ خطأ في حذف قاعدة البيانات:\n\n{ex.Message}\n\nتأكد من:\n• إغلاق جميع التطبيقات المتصلة بقاعدة البيانات\n• وجود صلاحيات إدارية\n• عدم استخدام قاعدة البيانات حالياً",
                            "خطأ في الحذف", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                    finally
                    {
                        button.IsEnabled = true;
                        button.Content = "🗑️";

                        // إعادة تفعيل جميع أزرار الحذف
                        foreach (var item in ListDatabases.Items)
                        {
                            var container = ListDatabases.ItemContainerGenerator.ContainerFromItem(item);
                            if (container != null)
                            {
                                var deleteBtn = FindVisualChild<Button>(container as DependencyObject);
                                if (deleteBtn != null)
                                {
                                    deleteBtn.IsEnabled = true;
                                }
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// البحث عن عنصر فرعي في الشجرة المرئية
        /// </summary>
        private static T FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < System.Windows.Media.VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = System.Windows.Media.VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    return result;

                var childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }
            return null;
        }
    }
}
