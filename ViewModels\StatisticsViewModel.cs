using System;
using System.Windows;
using Prism.Commands;
using Prism.Mvvm;

namespace DriverManagementSystem.ViewModels
{
    public class StatisticsViewModel : BindableBase
    {
        public StatisticsViewModel()
        {
            NavigateToReportCommand = new DelegateCommand(NavigateToReport);
            NavigateToContractCommand = new DelegateCommand(NavigateToContract);
        }

        public DelegateCommand NavigateToReportCommand { get; }
        public DelegateCommand NavigateToContractCommand { get; }

        private void NavigateToReport()
        {
            try
            {
                // فتح نافذة تقرير المحضر
                var reportWindow = new Views.ReportWindow();
                reportWindow.ShowDialog();

                // إظهار رسالة توضيحية للمستخدم
                MessageBox.Show("💡 نصيحة: لإنشاء تقرير لزيارة محددة، انتقل إلى قسم 'البيانات' واضغط على زر 'إنشاء تقرير' بجانب الزيارة المطلوبة.",
                    "معلومة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقرير المحضر: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void NavigateToContract()
        {
            try
            {
                // سيتم إنشاء نافذة عقد السائق لاحقاً
                MessageBox.Show("سيتم إضافة عقد السائق قريباً...", "قريباً",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح عقد السائق: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
