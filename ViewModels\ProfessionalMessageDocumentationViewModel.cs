using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using Microsoft.Win32;
using Prism.Commands;
using Prism.Mvvm;
using DriverManagementSystem.Models;
using DriverManagementSystem.Data;
using DriverManagementSystem.Views;

using System.Collections.Generic;

namespace DriverManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel احترافي لتوثيق الرسائل النصية مع إدارة متطورة للصور
    /// </summary>
    public class ProfessionalMessageDocumentationViewModel : BindableBase
    {
        private readonly ApplicationDbContext _context;
        private MessageDocumentation _documentation;
        private FieldVisit _selectedVisit;
        private string _lastSelectedFolder;

        public ProfessionalMessageDocumentationViewModel(FieldVisit selectedVisit)
        {
            _context = new ApplicationDbContext();
            _selectedVisit = selectedVisit;
            
            InitializeDocumentation();
            InitializeCommands();
            LoadExistingDocumentation();
            LoadLastSelectedFolder();
        }

        #region Properties

        public MessageDocumentation Documentation
        {
            get => _documentation;
            set => SetProperty(ref _documentation, value);
        }

        public FieldVisit SelectedVisit
        {
            get => _selectedVisit;
            set => SetProperty(ref _selectedVisit, value);
        }

        public ObservableCollection<string> ImagePaths { get; set; } = new ObservableCollection<string>();
        public ObservableCollection<MessageAttachment> OtherAttachments { get; set; } = new ObservableCollection<MessageAttachment>();

        private string _reportNumber;
        public string ReportNumber
        {
            get => _reportNumber;
            set => SetProperty(ref _reportNumber, value);
        }

        // تم إلغاء رقم العقد

        private string _firstOfficer;
        public string FirstOfficer
        {
            get => _firstOfficer;
            set => SetProperty(ref _firstOfficer, value);
        }

        private string _secondOfficer;
        public string SecondOfficer
        {
            get => _secondOfficer;
            set => SetProperty(ref _secondOfficer, value);
        }

        private string _thirdOfficer;
        public string ThirdOfficer
        {
            get => _thirdOfficer;
            set => SetProperty(ref _thirdOfficer, value);
        }

        private string _notes;
        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        private string _imagesFolderPath;
        public string ImagesFolderPath
        {
            get => _imagesFolderPath;
            set => SetProperty(ref _imagesFolderPath, value);
        }

        #endregion

        #region Commands

        public ICommand SelectImagesFolderCommand { get; private set; }
        public ICommand AddSingleImageCommand { get; private set; }
        public ICommand RemoveImageCommand { get; private set; }
        public ICommand AddOtherAttachmentCommand { get; private set; }
        public ICommand RemoveOtherAttachmentCommand { get; private set; }
        public ICommand SaveDocumentationCommand { get; private set; }
        public ICommand PreviewReportCommand { get; private set; }
        public ICommand PrintReportCommand { get; private set; }

        #endregion

        private void InitializeDocumentation()
        {
            Documentation = new MessageDocumentation
            {
                VisitNumber = _selectedVisit?.VisitNumber ?? "",
                DocumentationDate = DateTime.Now,
                ReportNumber = GenerateReportNumber(),
                // تم إلغاء رقم العقد
            };

            ReportNumber = Documentation.ReportNumber;
            // تم إلغاء رقم العقد
        }

        private void InitializeCommands()
        {
            SelectImagesFolderCommand = new DelegateCommand(SelectImagesFolder);
            AddSingleImageCommand = new DelegateCommand(AddSingleImage);
            RemoveImageCommand = new DelegateCommand<string>(RemoveImage);
            AddOtherAttachmentCommand = new DelegateCommand(AddOtherAttachment);
            RemoveOtherAttachmentCommand = new DelegateCommand<MessageAttachment>(RemoveOtherAttachment);
            SaveDocumentationCommand = new DelegateCommand(SaveDocumentation);
            PreviewReportCommand = new DelegateCommand(PreviewReport);
            PrintReportCommand = new DelegateCommand(PrintReport);
        }

        private string GenerateReportNumber()
        {
            var date = DateTime.Now;
            return $"MSG-{date.Year}{date.Month:D2}{date.Day:D2}-{_selectedVisit?.VisitNumber ?? "001"}";
        }

        // تم إلغاء دالة توليد رقم العقد

        /// <summary>
        /// اختيار مجلد الصور (مثل كود Access)
        /// </summary>
        private void SelectImagesFolder()
        {
            try
            {
                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "اختيار مجلد الصور - اختر أي ملف في المجلد المطلوب",
                    Filter = "جميع الملفات (*.*)|*.*",
                    CheckFileExists = false,
                    CheckPathExists = true,
                    Multiselect = false
                };

                // استخدام آخر مجلد محفوظ
                if (!string.IsNullOrEmpty(_lastSelectedFolder) && Directory.Exists(_lastSelectedFolder))
                {
                    openFileDialog.InitialDirectory = _lastSelectedFolder;
                }

                if (openFileDialog.ShowDialog() == true)
                {
                    var folderPath = Path.GetDirectoryName(openFileDialog.FileName);
                    if (!string.IsNullOrEmpty(folderPath))
                    {
                        ImagesFolderPath = folderPath;

                        // حفظ المسار لاستخدامه لاحقاً
                        SaveLastSelectedFolder(folderPath);

                        // تحميل الصور من المجلد
                        LoadImagesFromFolder(folderPath);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختيار مجلد الصور: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل الصور من المجلد المحدد
        /// </summary>
        private void LoadImagesFromFolder(string folderPath)
        {
            try
            {
                ImagePaths.Clear();
                
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".bmp", ".gif" };
                var imageFiles = Directory.GetFiles(folderPath)
                    .Where(file => allowedExtensions.Contains(Path.GetExtension(file).ToLower()))
                    .Take(6) // أقصى 6 صور
                    .ToList();

                foreach (var imagePath in imageFiles)
                {
                    ImagePaths.Add(imagePath);
                }

                // تحديث مسارات الصور في النموذج
                UpdateImagePathsInModel();

                MessageBox.Show($"تم تحميل {ImagePaths.Count} صورة من المجلد", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الصور: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إضافة صورة واحدة
        /// </summary>
        private void AddSingleImage()
        {
            try
            {
                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "اختيار صورة",
                    Filter = "ملفات الصور (*.jpg;*.jpeg;*.png;*.bmp;*.gif)|*.jpg;*.jpeg;*.png;*.bmp;*.gif",
                    Multiselect = false
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    if (ImagePaths.Count < 6)
                    {
                        ImagePaths.Add(openFileDialog.FileName);
                        UpdateImagePathsInModel();
                    }
                    else
                    {
                        MessageBox.Show("لا يمكن إضافة أكثر من 6 صور", "تنبيه", 
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الصورة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث مسارات الصور في النموذج
        /// </summary>
        private void UpdateImagePathsInModel()
        {
            Documentation.ImagePath1 = ImagePaths.Count > 0 ? ImagePaths[0] : null;
            Documentation.ImagePath2 = ImagePaths.Count > 1 ? ImagePaths[1] : null;
            Documentation.ImagePath3 = ImagePaths.Count > 2 ? ImagePaths[2] : null;
            Documentation.ImagePath4 = ImagePaths.Count > 3 ? ImagePaths[3] : null;
            Documentation.ImagePath5 = ImagePaths.Count > 4 ? ImagePaths[4] : null;
            Documentation.ImagePath6 = ImagePaths.Count > 5 ? ImagePaths[5] : null;
            Documentation.ImagesCount = ImagePaths.Count;
            Documentation.ImagesFolderPath = ImagesFolderPath;
        }

        private void RemoveImage(string imagePath)
        {
            if (!string.IsNullOrEmpty(imagePath) && ImagePaths.Contains(imagePath))
            {
                ImagePaths.Remove(imagePath);
                UpdateImagePathsInModel();
            }
        }

        private void AddOtherAttachment()
        {
            try
            {
                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "اختيار مرفق",
                    Filter = "جميع الملفات (*.*)|*.*|مستندات PDF (*.pdf)|*.pdf|مستندات Word (*.doc;*.docx)|*.doc;*.docx",
                    Multiselect = true
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    foreach (var fileName in openFileDialog.FileNames)
                    {
                        var fileInfo = new FileInfo(fileName);
                        var attachment = new MessageAttachment
                        {
                            FileName = fileInfo.Name,
                            FilePath = fileName,
                            FileType = fileInfo.Extension,
                            FileSize = fileInfo.Length,
                            Description = $"مرفق {OtherAttachments.Count + 1}"
                        };

                        OtherAttachments.Add(attachment);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المرفق: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RemoveOtherAttachment(MessageAttachment attachment)
        {
            if (attachment != null)
            {
                OtherAttachments.Remove(attachment);
            }
        }

        private void LoadExistingDocumentation()
        {
            try
            {
                var existing = _context.MessageDocumentations
                    .FirstOrDefault(d => d.VisitNumber == _selectedVisit.VisitNumber);

                if (existing != null)
                {
                    Documentation = existing;
                    ReportNumber = existing.ReportNumber;
                    // تم إلغاء رقم العقد
                    FirstOfficer = existing.FirstOfficer;
                    SecondOfficer = existing.SecondOfficer;
                    ThirdOfficer = existing.ThirdOfficer;
                    Notes = existing.Notes;
                    ImagesFolderPath = existing.ImagesFolderPath;

                    // تحميل مسارات الصور
                    LoadImagePathsFromModel();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل التوثيق الموجود: {ex.Message}");
            }
        }

        private void LoadImagePathsFromModel()
        {
            ImagePaths.Clear();
            
            if (!string.IsNullOrEmpty(Documentation.ImagePath1)) ImagePaths.Add(Documentation.ImagePath1);
            if (!string.IsNullOrEmpty(Documentation.ImagePath2)) ImagePaths.Add(Documentation.ImagePath2);
            if (!string.IsNullOrEmpty(Documentation.ImagePath3)) ImagePaths.Add(Documentation.ImagePath3);
            if (!string.IsNullOrEmpty(Documentation.ImagePath4)) ImagePaths.Add(Documentation.ImagePath4);
            if (!string.IsNullOrEmpty(Documentation.ImagePath5)) ImagePaths.Add(Documentation.ImagePath5);
            if (!string.IsNullOrEmpty(Documentation.ImagePath6)) ImagePaths.Add(Documentation.ImagePath6);
        }

        private void LoadLastSelectedFolder()
        {
            try
            {
                _lastSelectedFolder = Microsoft.Win32.Registry.GetValue(
                    @"HKEY_CURRENT_USER\Software\SFDSystem\MessageDocumentation", 
                    "LastFolder", 
                    "") as string;
            }
            catch
            {
                _lastSelectedFolder = "";
            }
        }

        private void SaveLastSelectedFolder(string folderPath)
        {
            try
            {
                Microsoft.Win32.Registry.SetValue(
                    @"HKEY_CURRENT_USER\Software\SFDSystem\MessageDocumentation", 
                    "LastFolder", 
                    folderPath);
                _lastSelectedFolder = folderPath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ مسار المجلد: {ex.Message}");
            }
        }

        private void SaveDocumentation()
        {
            try
            {
                Documentation.ReportNumber = ReportNumber;
                // تم إلغاء رقم العقد
                Documentation.FirstOfficer = FirstOfficer;
                Documentation.SecondOfficer = SecondOfficer;
                Documentation.ThirdOfficer = ThirdOfficer;
                Documentation.Notes = Notes;
                Documentation.LastModified = DateTime.Now;

                // تحديث مسارات الصور
                UpdateImagePathsInModel();

                // حفظ المرفقات الأخرى
                Documentation.Attachments.Clear();
                foreach (var attachment in OtherAttachments)
                {
                    Documentation.Attachments.Add(attachment);
                }

                // التحقق من وجود سجل موجود
                var existing = _context.MessageDocumentations
                    .FirstOrDefault(d => d.VisitNumber == Documentation.VisitNumber);

                if (existing != null)
                {
                    // تحديث السجل الموجود
                    _context.Entry(existing).CurrentValues.SetValues(Documentation);
                }
                else
                {
                    // إضافة سجل جديد
                    _context.MessageDocumentations.Add(Documentation);
                }

                _context.SaveChanges();

                MessageBox.Show("تم حفظ التوثيق بنجاح", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التوثيق: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PreviewReport()
        {
            try
            {
                // تم إزالة ProfessionalMessageDocumentationReportWindow - استخدم النافذة البديلة
                // إنشاء زيارة وهمية للمعاينة
                var dummyVisit = new FieldVisit { VisitNumber = Documentation.VisitNumber };
                var previewWindow = new PowerfulMessageDocumentationWindow(dummyVisit);
                previewWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintReport()
        {
            try
            {
                MessageBox.Show("سيتم تنفيذ الطباعة قريباً", "قيد التطوير", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
