using System;
using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.Models;
using DriverManagementSystem.ViewModels;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// Interaction logic for SimpleAssignmentWindow.xaml
    /// </summary>
    public partial class SimpleAssignmentWindow : Window
    {
        private readonly FieldVisit _selectedVisit;
        private readonly SimpleAssignmentViewModel _viewModel;

        public SimpleAssignmentWindow(FieldVisit selectedVisit)
        {
            InitializeComponent();
            _selectedVisit = selectedVisit;

            // إنشاء وربط ViewModel
            _viewModel = new SimpleAssignmentViewModel(selectedVisit);
            DataContext = _viewModel;

            // تحديث العنوان
            Title = $"التكليف - زيارة {selectedVisit?.VisitNumber ?? "001"}";
        }



        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
