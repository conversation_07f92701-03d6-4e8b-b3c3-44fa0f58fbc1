using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Web;

namespace SFDSystem.Services
{
    /// <summary>
    /// خدمة إرسال الرسائل عبر الواتس اب
    /// </summary>
    public class WhatsAppService : IDisposable
    {
        private const string WHATSAPP_DESKTOP_PROTOCOL = "whatsapp://send";
        private const string WHATSAPP_WEB_URL = "https://web.whatsapp.com/send";

        private readonly WhatsAppAutomationService _automationService;
        private bool _useAutomaticSending = true;

        /// <summary>
        /// إنشاء خدمة الواتس اب
        /// </summary>
        public WhatsAppService()
        {
            _automationService = new WhatsAppAutomationService();
        }

        /// <summary>
        /// تعيين نمط الإرسال
        /// </summary>
        /// <param name="useAutomatic">true للإرسال التلقائي، false للإرسال اليدوي</param>
        public void SetSendingMode(bool useAutomatic)
        {
            _useAutomaticSending = useAutomatic;
            System.Diagnostics.Debug.WriteLine($"📋 Sending mode set to: {(useAutomatic ? "Automatic" : "Manual")}");
        }

        /// <summary>
        /// إرسال رسالة عبر الواتس اب
        /// </summary>
        /// <param name="phoneNumber">رقم الهاتف (مع رمز الدولة)</param>
        /// <param name="message">نص الرسالة</param>
        /// <returns>true إذا تم الإرسال بنجاح</returns>
        public async Task<bool> SendMessageAsync(string phoneNumber, string message)
        {
            try
            {
                // تنظيف رقم الهاتف
                string cleanPhoneNumber = CleanPhoneNumber(phoneNumber);

                if (string.IsNullOrEmpty(cleanPhoneNumber))
                {
                    throw new ArgumentException("رقم الهاتف غير صحيح");
                }

                System.Diagnostics.Debug.WriteLine($"📱 Sending WhatsApp message to {cleanPhoneNumber} (Mode: {(_useAutomaticSending ? "Auto" : "Manual")})");

                // اختيار طريقة الإرسال
                if (_useAutomaticSending)
                {
                    System.Diagnostics.Debug.WriteLine("🤖 Attempting automatic sending...");

                    // الإرسال التلقائي باستخدام Selenium
                    bool automaticAttempted = await _automationService.SendMessageAutomaticallyAsync(cleanPhoneNumber, message);

                    if (automaticAttempted)
                    {
                        System.Diagnostics.Debug.WriteLine("⚠️ Automatic sending attempted - requires manual verification");
                        // حتى لو نجح الإرسال التلقائي، نحتاج للتحقق اليدوي
                        // لأن النظام الحالي لا يمكنه التحقق من الإرسال الفعلي
                        return true; // نعيد true لأن المحاولة تمت، لكن المستخدم سيحصل على تحذير
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("⚠️ Automatic sending failed, falling back to manual");
                        // في حالة فشل الإرسال التلقائي، استخدم الطريقة اليدوية
                        return await SendManuallyAsync(cleanPhoneNumber, message);
                    }
                }
                else
                {
                    // الإرسال اليدوي (الطريقة الأصلية)
                    System.Diagnostics.Debug.WriteLine("👤 Starting manual sending...");
                    return await SendManuallyAsync(cleanPhoneNumber, message);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error sending WhatsApp message: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الإرسال اليدوي (الطريقة الأصلية) - يفتح الواتس اب فقط، الإرسال الفعلي يحتاج تدخل يدوي
        /// </summary>
        private async Task<bool> SendManuallyAsync(string phoneNumber, string message)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"📱 Starting manual WhatsApp opening for {phoneNumber}");

                // محاولة فتح الواتس اب Desktop أولاً
                bool desktopOpened = await TryOpenWhatsAppDesktop(phoneNumber, message);

                if (desktopOpened)
                {
                    System.Diagnostics.Debug.WriteLine("✅ WhatsApp Desktop opened - manual completion required");
                    // نعيد true لأن الواتس اب فُتح، لكن الإرسال يحتاج إكمال يدوي
                    return true;
                }

                // إذا فشل Desktop، استخدم Web
                bool webOpened = await TryOpenWhatsAppWeb(phoneNumber, message);

                if (webOpened)
                {
                    System.Diagnostics.Debug.WriteLine("✅ WhatsApp Web opened - manual completion required");
                    return true;
                }

                System.Diagnostics.Debug.WriteLine("❌ Failed to open both WhatsApp Desktop and Web");
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error in manual sending: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// محاولة فتح الواتس اب Desktop
        /// </summary>
        private async Task<bool> TryOpenWhatsAppDesktop(string phoneNumber, string message)
        {
            try
            {
                // محاولة فتح الواتس اب Desktop مباشرة
                System.Diagnostics.Debug.WriteLine("🔍 Trying to open WhatsApp Desktop...");

                // إنشاء URL للواتس اب Desktop
                string encodedMessage = Uri.EscapeDataString(message);
                string whatsappUrl = $"{WHATSAPP_DESKTOP_PROTOCOL}?phone={phoneNumber}&text={encodedMessage}";

                System.Diagnostics.Debug.WriteLine($"🚀 Opening WhatsApp Desktop: {whatsappUrl}");

                // محاولة فتح الواتس اب
                var startInfo = new ProcessStartInfo
                {
                    FileName = whatsappUrl,
                    UseShellExecute = true
                };

                Process.Start(startInfo);

                // انتظار قصير للتأكد من فتح التطبيق
                await Task.Delay(2000);

                System.Diagnostics.Debug.WriteLine("✅ WhatsApp Desktop opened successfully");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Failed to open WhatsApp Desktop: {ex.Message}");

                // محاولة أخرى بطريقة مختلفة
                return await TryAlternativeWhatsAppDesktop(phoneNumber, message);
            }
        }

        /// <summary>
        /// محاولة بديلة لفتح الواتس اب Desktop
        /// </summary>
        private async Task<bool> TryAlternativeWhatsAppDesktop(string phoneNumber, string message)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 Trying alternative WhatsApp Desktop method...");

                // محاولة فتح الواتس اب عبر مسار التطبيق المباشر
                string[] possiblePaths = {
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                                "WhatsApp", "WhatsApp.exe"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles),
                                "WhatsApp", "WhatsApp.exe"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86),
                                "WhatsApp", "WhatsApp.exe")
                };

                foreach (string path in possiblePaths)
                {
                    if (File.Exists(path))
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ Found WhatsApp at: {path}");

                        // فتح التطبيق أولاً
                        Process.Start(new ProcessStartInfo
                        {
                            FileName = path,
                            UseShellExecute = true
                        });

                        // انتظار لفتح التطبيق
                        await Task.Delay(3000);

                        // ثم محاولة فتح المحادثة
                        string encodedMessage = Uri.EscapeDataString(message);
                        string whatsappUrl = $"{WHATSAPP_DESKTOP_PROTOCOL}?phone={phoneNumber}&text={encodedMessage}";

                        Process.Start(new ProcessStartInfo
                        {
                            FileName = whatsappUrl,
                            UseShellExecute = true
                        });

                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Alternative method failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// محاولة فتح الواتس اب Web
        /// </summary>
        private async Task<bool> TryOpenWhatsAppWeb(string phoneNumber, string message)
        {
            try
            {
                // إنشاء URL للواتس اب Web
                string encodedMessage = Uri.EscapeDataString(message);
                string whatsappUrl = $"{WHATSAPP_WEB_URL}?phone={phoneNumber}&text={encodedMessage}";

                System.Diagnostics.Debug.WriteLine($"🌐 Opening WhatsApp Web: {whatsappUrl}");

                // فتح المتصفح
                var startInfo = new ProcessStartInfo
                {
                    FileName = whatsappUrl,
                    UseShellExecute = true
                };

                Process.Start(startInfo);

                // انتظار قصير
                await Task.Delay(1000);

                System.Diagnostics.Debug.WriteLine("✅ WhatsApp Web opened successfully");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Failed to open WhatsApp Web: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود تطبيق الواتس اب Desktop
        /// </summary>
        private bool IsWhatsAppDesktopInstalled()
        {
            try
            {
                // مسارات محتملة لتطبيق الواتس اب
                string[] possiblePaths = {
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), 
                                "WhatsApp", "WhatsApp.exe"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), 
                                "WhatsApp", "WhatsApp.exe"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), 
                                "WhatsApp", "WhatsApp.exe")
                };

                foreach (string path in possiblePaths)
                {
                    if (File.Exists(path))
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ Found WhatsApp at: {path}");
                        return true;
                    }
                }

                // محاولة أخرى باستخدام Registry أو Windows Store
                return CheckWhatsAppFromRegistry();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error checking WhatsApp installation: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من الواتس اب عبر Registry
        /// </summary>
        private bool CheckWhatsAppFromRegistry()
        {
            try
            {
                // محاولة تشغيل أمر للتحقق من وجود الواتس اب
                var startInfo = new ProcessStartInfo
                {
                    FileName = "cmd.exe",
                    Arguments = "/c where whatsapp",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(startInfo))
                {
                    process.WaitForExit(3000);
                    string output = process.StandardOutput.ReadToEnd();
                    
                    if (!string.IsNullOrEmpty(output) && output.Contains("whatsapp"))
                    {
                        System.Diagnostics.Debug.WriteLine("✅ WhatsApp found via command line");
                        return true;
                    }
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// تنظيف رقم الهاتف
        /// </summary>
        private string CleanPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return string.Empty;

            // إزالة المسافات والرموز غير المرغوبة
            string cleaned = phoneNumber.Replace(" ", "")
                                      .Replace("-", "")
                                      .Replace("(", "")
                                      .Replace(")", "")
                                      .Replace("+", "");

            // إضافة رمز السعودية إذا لم يكن موجوداً
            if (!cleaned.StartsWith("966") && cleaned.StartsWith("05"))
            {
                cleaned = "966" + cleaned.Substring(1);
            }
            else if (!cleaned.StartsWith("966") && cleaned.StartsWith("5"))
            {
                cleaned = "966" + cleaned;
            }

            System.Diagnostics.Debug.WriteLine($"📱 Cleaned phone number: {phoneNumber} -> {cleaned}");
            return cleaned;
        }

        /// <summary>
        /// إرسال رسائل متعددة
        /// </summary>
        public async Task<int> SendMultipleMessagesAsync(System.Collections.Generic.List<(string phoneNumber, string message)> messages)
        {
            int successCount = 0;
            
            foreach (var (phoneNumber, message) in messages)
            {
                try
                {
                    bool success = await SendMessageAsync(phoneNumber, message);
                    if (success)
                    {
                        successCount++;
                        // انتظار بين الرسائل لتجنب الإرهاق
                        await Task.Delay(3000);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ Failed to send message to {phoneNumber}: {ex.Message}");
                }
            }

            return successCount;
        }

        /// <summary>
        /// عرض رسالة تأكيد للمستخدم
        /// </summary>
        public void ShowSuccessMessage(string driverName, bool isGroup = false, int count = 1)
        {
            string message;

            if (_useAutomaticSending)
            {
                message = isGroup
                    ? $"🤖 تم إرسال {count} رسالة تلقائياً عبر الواتس اب!\n\n✅ الرسائل تم إرسالها بنجاح\n\n💡 تم استخدام الإرسال التلقائي"
                    : $"🤖 تم إرسال الرسالة تلقائياً للسائق {driverName}!\n\n✅ الرسالة تم إرسالها بنجاح عبر الواتس اب\n\n💡 تم استخدام الإرسال التلقائي";
            }
            else
            {
                message = isGroup
                    ? $"✅ تم فتح الواتس اب لإرسال {count} رسالة\n\n📱 يرجى إكمال الإرسال من تطبيق الواتس اب\n\n💡 نصيحة: ستفتح نوافذ متعددة للواتس اب، يرجى إكمال الإرسال في كل نافذة"
                    : $"✅ تم فتح الواتس اب لإرسال رسالة للسائق {driverName}\n\n📱 يرجى إكمال الإرسال من تطبيق الواتس اب\n\n💡 الرسالة والرقم تم تحضيرهما مسبقاً، اضغط إرسال فقط";
            }

            string title = _useAutomaticSending ? "🤖 تم الإرسال التلقائي" : "🚀 تم فتح الواتس اب";
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// عرض رسالة خطأ
        /// </summary>
        public void ShowErrorMessage(string error)
        {
            MessageBox.Show($"❌ خطأ في إرسال الرسالة عبر الواتس اب:\n\n{error}\n\n💡 تأكد من:\n• تثبيت تطبيق الواتس اب على سطح المكتب\n• تسجيل الدخول في الواتس اب\n• صحة رقم الهاتف",
                          "⚠️ خطأ في الإرسال", MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            try
            {
                // لا حاجة لتنظيف WhatsAppAutomationService بعد الآن
                System.Diagnostics.Debug.WriteLine("🧹 WhatsApp service resources cleaned up");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ Error disposing WhatsApp service: {ex.Message}");
            }
        }
    }
}
