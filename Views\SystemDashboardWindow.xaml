<Window x:Class="DriverManagementSystem.Views.SystemDashboardWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="🚀 لوحة التحكم المتقدمة - نظام إدارة السائقين" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize">

    <Window.Resources>
        <Style TargetType="Border" x:Key="DashboardCardStyle">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="4" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="TextBlock" x:Key="CardTitleStyle">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style TargetType="TextBlock" x:Key="MetricNumberStyle">
            <Setter Property="FontSize" Value="28"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
        </Style>

        <Style TargetType="TextBlock" x:Key="MetricLabelStyle">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Foreground" Value="#666"/>
        </Style>
    </Window.Resources>

    <Grid Background="Linear Gradient(135deg, #667eea 0%, #764ba2 100%)">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Padding="30,20" Background="Transparent">
            <StackPanel>
                <TextBlock Text="🚀 لوحة التحكم المتقدمة" 
                          FontSize="28" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="مراقبة شاملة ومتقدمة لجميع عمليات النظام" 
                          FontSize="14" Foreground="#E8E8E8" HorizontalAlignment="Center" Margin="0,8,0,0"/>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                    <TextBlock Text="آخر تحديث:" FontSize="12" Foreground="#E8E8E8" Margin="0,0,8,0"/>
                    <TextBlock Text="{Binding LastUpdateTime, StringFormat=HH:mm:ss}" 
                              FontSize="12" FontWeight="Bold" Foreground="White"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Quick Stats -->
        <Grid Grid.Row="1" Margin="20,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- System Status -->
            <Border Grid.Column="0" Style="{StaticResource DashboardCardStyle}">
                <StackPanel>
                    <TextBlock Text="⚡ حالة النظام" Style="{StaticResource CardTitleStyle}" Foreground="#4CAF50"/>
                    <TextBlock Text="{Binding SystemStatus}" Style="{StaticResource MetricNumberStyle}" Foreground="#4CAF50"/>
                    <TextBlock Text="متصل" Style="{StaticResource MetricLabelStyle}"/>
                </StackPanel>
            </Border>

            <!-- Active Users -->
            <Border Grid.Column="1" Style="{StaticResource DashboardCardStyle}">
                <StackPanel>
                    <TextBlock Text="👥 المستخدمين النشطين" Style="{StaticResource CardTitleStyle}" Foreground="#2196F3"/>
                    <TextBlock Text="{Binding ActiveUsers}" Style="{StaticResource MetricNumberStyle}" Foreground="#2196F3"/>
                    <TextBlock Text="مستخدم" Style="{StaticResource MetricLabelStyle}"/>
                </StackPanel>
            </Border>

            <!-- Database Size -->
            <Border Grid.Column="2" Style="{StaticResource DashboardCardStyle}">
                <StackPanel>
                    <TextBlock Text="💾 حجم قاعدة البيانات" Style="{StaticResource CardTitleStyle}" Foreground="#FF9800"/>
                    <TextBlock Text="{Binding DatabaseSize}" Style="{StaticResource MetricNumberStyle}" Foreground="#FF9800"/>
                    <TextBlock Text="ميجابايت" Style="{StaticResource MetricLabelStyle}"/>
                </StackPanel>
            </Border>

            <!-- Import Success Rate -->
            <Border Grid.Column="3" Style="{StaticResource DashboardCardStyle}">
                <StackPanel>
                    <TextBlock Text="📊 معدل نجاح الاستيراد" Style="{StaticResource CardTitleStyle}" Foreground="#9C27B0"/>
                    <TextBlock Text="{Binding ImportSuccessRate, StringFormat={}{0:F1}%}" Style="{StaticResource MetricNumberStyle}" Foreground="#9C27B0"/>
                    <TextBlock Text="نجاح" Style="{StaticResource MetricLabelStyle}"/>
                </StackPanel>
            </Border>

            <!-- Backup Status -->
            <Border Grid.Column="4" Style="{StaticResource DashboardCardStyle}">
                <StackPanel>
                    <TextBlock Text="🔄 آخر نسخة احتياطية" Style="{StaticResource CardTitleStyle}" Foreground="#607D8B"/>
                    <TextBlock Text="{Binding LastBackupTime}" Style="{StaticResource MetricNumberStyle}" Foreground="#607D8B" FontSize="16"/>
                    <TextBlock Text="دقيقة مضت" Style="{StaticResource MetricLabelStyle}"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Main Content -->
        <Grid Grid.Row="2" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Charts and Logs -->
            <Grid Grid.Column="0" Margin="0,0,10,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- System Activity -->
                <Border Grid.Row="0" Style="{StaticResource DashboardCardStyle}" Margin="0,0,0,10">
                    <StackPanel>
                        <TextBlock Text="📈 نشاط النظام المباشر" Style="{StaticResource CardTitleStyle}" Foreground="#3F51B5"/>
                        
                        <ScrollViewer Height="200" VerticalScrollBarVisibility="Auto">
                            <ItemsControl ItemsSource="{Binding SystemLogs}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="#F8F9FA" CornerRadius="5" Padding="10" Margin="0,0,0,5">
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="{Binding Timestamp, StringFormat=HH:mm:ss}" 
                                                          FontWeight="Bold" Foreground="#666" Margin="0,0,10,0"/>
                                                <TextBlock Text="{Binding Icon}" FontSize="14" Margin="0,0,8,0"/>
                                                <TextBlock Text="{Binding Message}" TextWrapping="Wrap"/>
                                            </StackPanel>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </StackPanel>
                </Border>

                <!-- Performance Metrics -->
                <Border Grid.Row="1" Style="{StaticResource DashboardCardStyle}">
                    <StackPanel>
                        <TextBlock Text="⚡ مقاييس الأداء" Style="{StaticResource CardTitleStyle}" Foreground="#E91E63"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="استخدام الذاكرة:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ProgressBar Value="{Binding MemoryUsage}" Maximum="100" Height="20" 
                                           Background="#E0E0E0" Foreground="#4CAF50"/>
                                <TextBlock Text="{Binding MemoryUsage, StringFormat={}{0:F1}%}" 
                                          HorizontalAlignment="Center" FontSize="12" Margin="0,5,0,15"/>
                                
                                <TextBlock Text="استخدام المعالج:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ProgressBar Value="{Binding CpuUsage}" Maximum="100" Height="20" 
                                           Background="#E0E0E0" Foreground="#FF9800"/>
                                <TextBlock Text="{Binding CpuUsage, StringFormat={}{0:F1}%}" 
                                          HorizontalAlignment="Center" FontSize="12"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="20,0,0,0">
                                <TextBlock Text="العمليات النشطة:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding ActiveProcesses}" FontSize="24" FontWeight="Bold" 
                                          Foreground="#2196F3" HorizontalAlignment="Center"/>
                                
                                <TextBlock Text="الاتصالات:" FontWeight="Bold" Margin="0,15,0,5"/>
                                <TextBlock Text="{Binding ActiveConnections}" FontSize="24" FontWeight="Bold" 
                                          Foreground="#9C27B0" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Right Panel - Quick Actions -->
            <Border Grid.Column="1" Style="{StaticResource DashboardCardStyle}">
                <StackPanel>
                    <TextBlock Text="🛠️ إجراءات سريعة" Style="{StaticResource CardTitleStyle}" Foreground="#795548"/>
                    
                    <Button Content="🔄 إنشاء نسخة احتياطية فورية" 
                           Background="#4CAF50" Foreground="White" 
                           Padding="15,10" Margin="0,0,0,10" FontWeight="Bold"
                           Click="CreateBackupButton_Click"/>
                    
                    <Button Content="📊 عرض إحصائيات الاستيراد" 
                           Background="#2196F3" Foreground="White" 
                           Padding="15,10" Margin="0,0,0,10" FontWeight="Bold"
                           Click="ShowImportStatsButton_Click"/>
                    
                    <Button Content="🗑️ تنظيف الملفات المؤقتة" 
                           Background="#FF9800" Foreground="White" 
                           Padding="15,10" Margin="0,0,0,10" FontWeight="Bold"
                           Click="CleanTempFilesButton_Click"/>
                    
                    <Button Content="🔧 إعدادات النظام المتقدمة" 
                           Background="#9C27B0" Foreground="White" 
                           Padding="15,10" Margin="0,0,0,10" FontWeight="Bold"
                           Click="AdvancedSettingsButton_Click"/>
                    
                    <Button Content="📋 تصدير سجل النظام" 
                           Background="#607D8B" Foreground="White" 
                           Padding="15,10" Margin="0,0,0,10" FontWeight="Bold"
                           Click="ExportLogsButton_Click"/>
                    
                    <Separator Margin="0,15"/>
                    
                    <TextBlock Text="🔔 تنبيهات النظام" FontWeight="Bold" Margin="0,0,0,10"/>
                    <ScrollViewer Height="150" VerticalScrollBarVisibility="Auto">
                        <ItemsControl ItemsSource="{Binding SystemAlerts}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="#FFF3E0" CornerRadius="5" Padding="8" Margin="0,0,0,5">
                                        <StackPanel>
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="{Binding Icon}" FontSize="12" Margin="0,0,5,0"/>
                                                <TextBlock Text="{Binding Title}" FontWeight="Bold" FontSize="11"/>
                                            </StackPanel>
                                            <TextBlock Text="{Binding Message}" FontSize="10" 
                                                      TextWrapping="Wrap" Foreground="#666" Margin="0,2,0,0"/>
                                        </StackPanel>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Footer -->
        <Border Grid.Row="3" Background="Transparent" Padding="20,10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="🚀 نظام إدارة السائقين المتقدم" FontWeight="Bold" Foreground="White" Margin="0,0,20,0"/>
                <TextBlock Text="•" Foreground="#E8E8E8" Margin="0,0,20,0"/>
                <TextBlock Text="الإصدار 2.0" Foreground="#E8E8E8" Margin="0,0,20,0"/>
                <TextBlock Text="•" Foreground="#E8E8E8" Margin="0,0,20,0"/>
                <TextBlock Text="تطوير: فريق التطوير المتقدم" Foreground="#E8E8E8"/>
            </StackPanel>
        </Border>

    </Grid>
</Window>
