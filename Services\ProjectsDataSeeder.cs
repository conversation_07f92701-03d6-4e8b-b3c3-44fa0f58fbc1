using System;
using System.Threading.Tasks;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    public class ProjectsDataSeeder
    {
        private readonly IDataService _dataService;

        public ProjectsDataSeeder(IDataService dataService)
        {
            _dataService = dataService;
        }

        public async Task<int> SeedRemainingProjectsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📋 بدء إضافة باقي بيانات المشاريع...");

                // بيانات المشاريع - الجزء الثاني
                var projectsData2 = new[]
                {
                    new { Number = "911-13826", Name = "التدريب والاشراف على البيوت المحمية وشبكات الري - مديرية المنار -ذمار" },
                    new { Number = "911-13827", Name = "التدريب والاشراف على البيوت المحمية وشبكات الري - مديرية جهران -ذمار" },
                    new { Number = "911-13828", Name = "التدريب والاشراف على البيوت المحمية وشبكات الري - مديرية وصاب السافل -ذمار" },
                    new { Number = "911-13829", Name = "دعم وبناء قدرات مربي النحل في مديرية عتمة - ذمار" },
                    new { Number = "911-13830", Name = "دعم وبناء قدرات مربي النحل في مديرية المنار - ذمار" },
                    new { Number = "911-13831", Name = "دعم وبناء قدرات مربي النحل في مديرية وصاب السافل - ذمار" },
                    new { Number = "911-13832", Name = "التدريب والاشراف على البيوت المحمية وشبكات الري منحة الامن الغذائي عبر FAO - مديرية عتمة- ذمار" },
                    new { Number = "911-13833", Name = "دعم وبناء قدرات مزارعي الخضار منحة الاستجابة للامن الغذائي عبر FAO مديرية جهران- ذمار" },
                    new { Number = "911-13834", Name = "دعم وبناء قدرات مزارعي الخضار منحة الاستجابة للامن الغذائي عبر FAO مديرية عتمة- ذمار" },
                    new { Number = "911-13835", Name = "دعم وبناء قدرات مزارعي الخضار منحة الاستجابة للامن الغذائي عبر FAO مديرية المنار- ذمار" },
                    new { Number = "911-13836", Name = "بناء قدرات ودعم صغار مربي الثروة الحيوانية بالاعلاف في مديرية جهران - ذمار" },
                    new { Number = "911-13837", Name = "بناء قدرات ودعم صغار مربي الثروة الحيوانية بالاعلاف في مديرية وصاب السافل - ذمار" },
                    new { Number = "911-13838", Name = "بناء قدرات ودعم صغار مربي الثروة الحيوانية بالاعلاف في مديرية عتمة - ذمار" },
                    new { Number = "911-13839", Name = "بناء قدرات ودعم صغار مربي الثروة الحيوانية بالاعلاف في مديرية المنار - ذمار" },
                    new { Number = "911-13897", Name = "اعادة تأهيل وتشجير منطقة هران - مدينة ذمار - ذمار" },
                    new { Number = "911-13738", Name = "نشر الخدمات المالية وغير المالية في الريف عبر مجموعات VSLAs - محافظة البيضاء" },
                    new { Number = "911-13798", Name = "دعم الوصول الى الخدمات المالية وغير المالية في الريف عبر VSLAs - محافظة ذمار" },
                    new { Number = "911-13799", Name = "دعم الوصول الى الخدمات المالية وغير المالية في الريف عبر VSLAs - محافظة البيضاء" },
                    new { Number = "911-13755", Name = "التدريب الفني والمهارات الحياتية لأعضاء مجموعات الادخار والتمويل الريفي - مديرية وصاب العالي - ذمار" },
                    new { Number = "911-13742", Name = "نشر الخدمات المالية وغير المالية في الريف عبر مجموعاتVSLAs، محافظة ذمار" },
                    new { Number = "911-13762", Name = "تفعيل وتحديد اولويات احتياجات المجتمع بالمشاركة للمنحة البريطانية الثانية - ذمار" },
                    new { Number = "911-13919", Name = "تفعيل وتحديد اولويات احتياجات المجتمع بالمشاركة للمنحة البريطانية الثانية - فرع ذمار" },
                    new { Number = "911-13882", Name = "تدريب استشاريين في مجال الحماية البيئية والإجتماعية والصحة والسلامة والنوع الاجتماعي - ذمار" },
                    new { Number = "911-13856", Name = "تفعيل وتحديد اولويات احتياجات المجتمع لمنحة البنك الدولي السابعة - البيضاء" },
                    new { Number = "911-13918", Name = "مشروع إنشاء و اعادة المدرجات الزراعيةالمتضرره من السيول لتجمع قرى الحصب وماجاورها-عزلة وادي الخشب-وصاب السافل-ذمار" },
                    new { Number = "911-13847", Name = "مشروع تأهيل طريق وتحسين الوضع البيئي وحصاد مياه الأمطار تجمع قرى العقيقة وما جاورها- عزلة- وادي زبيد- عنس-ذمار" },
                    new { Number = "100-13829", Name = "مشروع  تأهيل ورصف طريق وحصاد مياة وتاهيل المدرجات لقريتي المجازع والاهدر-عزلة شرقي جبل الشرق-جبل الشرق-ذمار" },
                    new { Number = "911-13775", Name = "مشروع تحسين طرق  وأنشاء  قنوات لري البن لقرية خيران وما جاورها -المساحره-الطفه -البيضاء" },
                    new { Number = "911-13776", Name = "مشروع تحسين طرق وتحسين الوضع البيئي وانشاء خزانات لتجمع قريتي الساره وسفيان-بني سويد-ضوران-ذمار" },
                    new { Number = "911-13845", Name = "حصاد مياه الامطار وتأهيل طريق وتحسين الوضع البيئي وتأهيل المراعي - قرية هيوه - صباح - صباح - البيضاء" },
                    new { Number = "911-13884", Name = "مشروع أنشاء خزانات تجميعيه وقنوات ري  واحواض لتجمع قرى عزلة ال منصور - عزلة/ آل منصور- مديرية/ ناطع- البيضاء" },
                    new { Number = "911-13883", Name = "حصاد مياه الامطار وحماية الأراضي وحواجز تهدئة وترسيب لقرية المصار الاسفل وماجاورها - وعالة آل رقاب -ناطع - البيضاء" },
                    new { Number = "911-13819", Name = "حصاد مياه الأمطار وتحسين الوضع البيئي وتأهيل طرق - قرية بني مسعود - قاعده - وصاب العالي - ذمار" },
                    new { Number = "911-13818", Name = "حصاد مياه الامطار وتحسين الوضع البيئي - لتجمع قرى ( المقبابة - أخنه - شويزان ) - الأجراف - وصاب السافل - ذمار" },
                    new { Number = "911-13817", Name = "مشروع صيانة قنوات الري وانشاء خزان تجميعي وحدائق مزلية لقرية بني العبسي وبني بحير- حمير ابزار -عتمه -ذمار" },
                    new { Number = "911-13816", Name = "تأهيل وتحسين طريق وحصاد مياه بقرية ظلمان - عزلة الرعية - المنار - ذمار" },
                    new { Number = "911-13910", Name = "مشروع حصاد مياه الامطار وحواجز تغذيه وأنشاء حدائق زراعيه لقرية وادي الكريف وما جاورها-بني سويد-ضوران-ذمار" },
                    new { Number = "911-13644", Name = "مشروع انشاء خزانات تجميعيه  و حماية الاراضي الزراعيه  ومزارع منزليه لقرية تبين -ال محن الظهره-القريشيه-البيضاء" },
                    new { Number = "911-13645", Name = "مشروع حصاد مياه الامطار واستصلاح الاراضي الزراعية لقرى (الحطب-القري-القابل) -قيفه-ال محن الجوف-القريشية-البيضاء" },
                    new { Number = "911-13925", Name = "تاهيل طريق قرضان الصنع - وصاب السافل - ذمار" },
                    new { Number = "911-13934", Name = "استكمال تأهيل طريق نويد - العر وتحسين المعيشة لتجمع قرى العر محلاتها - بني حاتم - ضوران - ذمار" },
                    new { Number = "911-13913", Name = "تأهيل طريق هجيم - رمادة -اللكمة - بني سويد - عتمة - ذمار." },
                    new { Number = "911-13886", Name = "تأهيل وتدريب استشاريين في المجالات المجتمعية والزراعية والهندسية لعدد (450) استشاري واستشارية" },
                    new { Number = "911-13933", Name = "تعزيز قدرات المجتمع المحلي وتحسين سبل العيش والتماسك الاجتماعي - برنامج التمكين ضوران بني سويد" },
                    new { Number = "911-13911", Name = "حصاد مياه وتأهيل مراعي لتجمع قرى شعب حلوه -ال غشام - مديرية الملاجم - البيضاء" },
                    new { Number = "911-13912", Name = "حصاد مياه وخزانات تجميعية وحماية الاراضي وتأهيل مراعي والطرق لقرية الذويدة والقرى المجاورة -السعيديه -الطفه-البيضاء" },
                    new { Number = "911-13708", Name = "مشروع أستصلاح وحماية اراضي زراعيه وأنشاء خزانات تجميعيه لقرية رواف وما جاورها -الاغوال السفلى-ردمان ال عوض-البيضاء" },
                    new { Number = "911-13929", Name = "تدريب في مجال الصحه والسلامه المهنيه - فرع ذمار" },
                    new { Number = "911-13924", Name = "دراسة الحالة التشغيلية للمشاريع المنجزة - فرع ذمار 2025م" },
                    new { Number = "911-13926", Name = "تنفيذ عدة مكونات لقرية حمة زبيد -جبل زبيد - عنس - ذمار" },
                    new { Number = "911-13927", Name = "مشروع عدة تدخلات متنوعة لقرية الرعينة محلات ( الرعينة -بيت زياد –الخطم-الموشع-ايفع-عجلانه-بيت قرعه-القدمة-الكولة )عزلة ماور -المنار -ذمار" },
                    new { Number = "911-13932", Name = "مشروع عدة تدخلات متنوعة لقرية الرعينة محلات ( مرب-شعب زيد –المعازيب -الحراير) عزلة ماور -المنار -ذمار" }
                };

                int addedCount = 0;
                foreach (var projectData in projectsData2)
                {
                    try
                    {
                        // إنشاء مشروع جديد
                        var project = new Project
                        {
                            ProjectNumber = projectData.Number,
                            ProjectName = projectData.Name,
                            CreatedAt = DateTime.Now,
                            IsActive = true
                        };

                        // إضافة المشروع
                        var success = await _dataService.AddProjectAsync(project);
                        if (success)
                        {
                            addedCount++;
                            System.Diagnostics.Debug.WriteLine($"✅ تم إضافة المشروع: {project.ProjectNumber}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ فشل في إضافة المشروع: {project.ProjectNumber}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة المشروع {projectData.Number}: {ex.Message}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم إضافة {addedCount} مشروع من الدفعة الثانية");
                return addedCount;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة باقي بيانات المشاريع: {ex.Message}");
                return 0;
            }
        }
    }
}
