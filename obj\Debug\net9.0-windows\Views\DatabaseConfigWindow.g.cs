﻿#pragma checksum "..\..\..\..\Views\DatabaseConfigWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F92B34188CD81F06D5C12DD91E135833BBA12F4F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DriverManagementSystem.Views {
    
    
    /// <summary>
    /// DatabaseConfigWindow
    /// </summary>
    public partial class DatabaseConfigWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 69 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton RadioServerDatabase;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton RadioLocalDatabase;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox ServerSettingsGroup;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtServerName;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtDatabaseName;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton RadioWindowsAuth;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton RadioSqlAuth;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel SqlAuthPanel;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtUsername;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox TxtPassword;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox LocalDatabaseGroup;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtLocalDatabaseName;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtLocalDatabasePath;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtLocalDbFullPath;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCancel;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSaveSettings;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCreateDatabase;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnTestConnection;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SFDSystem;V2.0.0.0;component/views/databaseconfigwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.RadioServerDatabase = ((System.Windows.Controls.RadioButton)(target));
            
            #line 70 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.RadioServerDatabase.Checked += new System.Windows.RoutedEventHandler(this.RadioServerDatabase_Checked);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RadioLocalDatabase = ((System.Windows.Controls.RadioButton)(target));
            
            #line 72 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.RadioLocalDatabase.Checked += new System.Windows.RoutedEventHandler(this.RadioLocalDatabase_Checked);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ServerSettingsGroup = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 4:
            this.TxtServerName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.TxtDatabaseName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.RadioWindowsAuth = ((System.Windows.Controls.RadioButton)(target));
            
            #line 118 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.RadioWindowsAuth.Checked += new System.Windows.RoutedEventHandler(this.RadioWindowsAuth_Checked);
            
            #line default
            #line hidden
            return;
            case 7:
            this.RadioSqlAuth = ((System.Windows.Controls.RadioButton)(target));
            
            #line 120 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.RadioSqlAuth.Checked += new System.Windows.RoutedEventHandler(this.RadioSqlAuth_Checked);
            
            #line default
            #line hidden
            return;
            case 8:
            this.SqlAuthPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 9:
            this.TxtUsername = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.TxtPassword = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 11:
            this.LocalDatabaseGroup = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 12:
            this.TxtLocalDatabaseName = ((System.Windows.Controls.TextBox)(target));
            
            #line 168 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.TxtLocalDatabaseName.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtLocalDatabaseName_TextChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.TxtLocalDatabasePath = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.TxtLocalDbFullPath = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.BtnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 191 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.BtnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.BtnSaveSettings = ((System.Windows.Controls.Button)(target));
            
            #line 193 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.BtnSaveSettings.Click += new System.Windows.RoutedEventHandler(this.BtnSaveSettings_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.BtnCreateDatabase = ((System.Windows.Controls.Button)(target));
            
            #line 195 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.BtnCreateDatabase.Click += new System.Windows.RoutedEventHandler(this.BtnCreateDatabase_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.BtnTestConnection = ((System.Windows.Controls.Button)(target));
            
            #line 197 "..\..\..\..\Views\DatabaseConfigWindow.xaml"
            this.BtnTestConnection.Click += new System.Windows.RoutedEventHandler(this.BtnTestConnection_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

