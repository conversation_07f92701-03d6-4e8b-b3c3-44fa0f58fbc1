using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة النماذج الاحترافية
    /// </summary>
    public interface IProfessionalTemplateService
    {
        Task<List<ProfessionalTemplate>> GetAllTemplatesAsync();
        Task<List<ProfessionalTemplate>> GetTemplatesByTypeAsync(string type);
        Task<ProfessionalTemplate?> GetTemplateByIdAsync(int id);
        Task<bool> CreateTemplateAsync(ProfessionalTemplate template);
        Task<bool> UpdateTemplateAsync(ProfessionalTemplate template);
        Task<bool> DeleteTemplateAsync(int id);
        Task<TemplateStatistics> GetStatisticsAsync();
        Task<List<ProfessionalTemplate>> SearchTemplatesAsync(string searchTerm);
        Task<bool> ActivateTemplateAsync(int id);
        Task<bool> DeactivateTemplateAsync(int id);
        Task<ProfessionalTemplate> CloneTemplateAsync(int id);
        Task<bool> UpdateUsageAsync(int id);
    }

    public class ProfessionalTemplateService : IProfessionalTemplateService
    {
        private readonly ApplicationDbContext _context;

        public ProfessionalTemplateService(ApplicationDbContext context)
        {
            _context = context;
        }

        public ProfessionalTemplateService()
        {
            _context = new ApplicationDbContext();
        }

        public async Task<List<ProfessionalTemplate>> GetAllTemplatesAsync()
        {
            try
            {
                return await _context.ProfessionalTemplates
                    .OrderByDescending(t => t.CreatedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب النماذج: {ex.Message}");
                return new List<ProfessionalTemplate>();
            }
        }

        public async Task<List<ProfessionalTemplate>> GetTemplatesByTypeAsync(string type)
        {
            try
            {
                return await _context.ProfessionalTemplates
                    .Where(t => t.Type == type && t.IsActive)
                    .OrderByDescending(t => t.CreatedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب النماذج حسب النوع: {ex.Message}");
                return new List<ProfessionalTemplate>();
            }
        }

        public async Task<ProfessionalTemplate?> GetTemplateByIdAsync(int id)
        {
            try
            {
                return await _context.ProfessionalTemplates
                    .FirstOrDefaultAsync(t => t.Id == id);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب النموذج: {ex.Message}");
                return null;
            }
        }

        public async Task<bool> CreateTemplateAsync(ProfessionalTemplate template)
        {
            try
            {
                template.CreatedDate = DateTime.Now;
                _context.ProfessionalTemplates.Add(template);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء النموذج: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> UpdateTemplateAsync(ProfessionalTemplate template)
        {
            try
            {
                template.ModifiedDate = DateTime.Now;
                _context.ProfessionalTemplates.Update(template);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث النموذج: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteTemplateAsync(int id)
        {
            try
            {
                var template = await GetTemplateByIdAsync(id);
                if (template != null && !template.IsDefault)
                {
                    _context.ProfessionalTemplates.Remove(template);
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف النموذج: {ex.Message}");
                return false;
            }
        }

        public async Task<TemplateStatistics> GetStatisticsAsync()
        {
            try
            {
                var templates = await _context.ProfessionalTemplates.ToListAsync();
                
                return new TemplateStatistics
                {
                    TotalTemplates = templates.Count,
                    UserTemplates = templates.Count(t => t.Type == "UserTemplate"),
                    MessageTemplates = templates.Count(t => t.Type == "MessageTemplate"),
                    ReportTemplates = templates.Count(t => t.Type == "ReportTemplate"),
                    ActiveTemplates = templates.Count(t => t.IsActive),
                    InactiveTemplates = templates.Count(t => !t.IsActive),
                    DefaultTemplates = templates.Count(t => t.IsDefault),
                    CustomTemplates = templates.Count(t => !t.IsDefault),
                    LastCreated = templates.Any() ? templates.Max(t => t.CreatedDate) : DateTime.MinValue,
                    LastUsed = templates.Where(t => t.LastUsedDate.HasValue).Any() ? 
                              templates.Where(t => t.LastUsedDate.HasValue).Max(t => t.LastUsedDate.Value) : DateTime.MinValue,
                    MostUsedTemplate = templates.OrderByDescending(t => t.UsageCount).FirstOrDefault()?.Name ?? "لا يوجد",
                    TotalUsage = templates.Sum(t => t.UsageCount)
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب الإحصائيات: {ex.Message}");
                return new TemplateStatistics();
            }
        }

        public async Task<List<ProfessionalTemplate>> SearchTemplatesAsync(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return await GetAllTemplatesAsync();

                return await _context.ProfessionalTemplates
                    .Where(t => t.Name.Contains(searchTerm) || 
                               t.Description.Contains(searchTerm) ||
                               t.Category.Contains(searchTerm) ||
                               (t.Tags != null && t.Tags.Contains(searchTerm)))
                    .OrderByDescending(t => t.CreatedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث: {ex.Message}");
                return new List<ProfessionalTemplate>();
            }
        }

        public async Task<bool> ActivateTemplateAsync(int id)
        {
            try
            {
                var template = await GetTemplateByIdAsync(id);
                if (template != null)
                {
                    template.IsActive = true;
                    template.ModifiedDate = DateTime.Now;
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تفعيل النموذج: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeactivateTemplateAsync(int id)
        {
            try
            {
                var template = await GetTemplateByIdAsync(id);
                if (template != null && !template.IsDefault)
                {
                    template.IsActive = false;
                    template.ModifiedDate = DateTime.Now;
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إلغاء تفعيل النموذج: {ex.Message}");
                return false;
            }
        }

        public async Task<ProfessionalTemplate> CloneTemplateAsync(int id)
        {
            try
            {
                var original = await GetTemplateByIdAsync(id);
                if (original != null)
                {
                    var clone = original.Clone();
                    await CreateTemplateAsync(clone);
                    return clone;
                }
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في نسخ النموذج: {ex.Message}");
                return null;
            }
        }

        public async Task<bool> UpdateUsageAsync(int id)
        {
            try
            {
                var template = await GetTemplateByIdAsync(id);
                if (template != null)
                {
                    template.UpdateLastUsed();
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الاستخدام: {ex.Message}");
                return false;
            }
        }
    }
}
