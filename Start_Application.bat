@echo off
echo ========================================
echo      نظام إدارة الزيارات الميدانية
echo      مع الإرسال التلقائي للواتس اب
echo ========================================
echo.

echo 🔍 فحص الجاهزية...

REM Quick check for automatic sending
if exist "chromedriver.exe" (
    echo ✅ الإرسال التلقائي جاهز
) else (
    echo ⚠️ الإرسال التلقائي غير جاهز (ChromeDriver مفقود)
    echo 💡 شغل Download_ChromeDriver.bat لتفعيل الإرسال التلقائي
)

echo.
echo 🚀 بدء تشغيل النظام...
echo.

REM Check if build is needed
if not exist "bin\Debug\net9.0-windows\SFDSystem.exe" (
    echo 🔧 بناء المشروع...
    dotnet build
    echo.
)

REM Run the application
echo ✅ تشغيل التطبيق...
echo.
echo 📱 للوصول للإرسال التلقائي:
echo    القائمة الرئيسية ← الرسائل الاحترافية ← WhatsApp ← إرسال تلقائي
echo.

dotnet run

echo.
echo ========================================
echo تم إغلاق التطبيق
pause
