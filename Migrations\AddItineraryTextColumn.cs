using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using System;
using System.Threading.Tasks;

namespace DriverManagementSystem.Migrations
{
    /// <summary>
    /// Migration لإضافة عمود خط السير النصي
    /// </summary>
    public static class AddItineraryTextColumn
    {
        /// <summary>
        /// تطبيق Migration لإضافة العمود الجديد
        /// </summary>
        public static async Task ApplyMigrationAsync(DbContext context)
        {
            try
            {
                // التحقق من وجود العمود أولاً
                var columnExists = await CheckColumnExistsAsync(context, "FieldVisits", "ItineraryText");
                
                if (!columnExists)
                {
                    // إضافة العمود الجديد
                    var sql = @"
                        ALTER TABLE FieldVisits 
                        ADD COLUMN ItineraryText TEXT NULL;
                    ";
                    
                    await context.Database.ExecuteSqlRawAsync(sql);
                    
                    System.Diagnostics.Debug.WriteLine("✅ تم إضافة عمود ItineraryText بنجاح");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ عمود ItineraryText موجود بالفعل");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تطبيق Migration: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// التحقق من وجود عمود في الجدول
        /// </summary>
        private static async Task<bool> CheckColumnExistsAsync(DbContext context, string tableName, string columnName)
        {
            try
            {
                var result = await context.Database.SqlQueryRaw<int>(
                    "SELECT COUNT(*) FROM pragma_table_info('FieldVisits') WHERE name = 'ItineraryText'"
                ).FirstOrDefaultAsync();

                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التحقق من وجود العمود: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التراجع عن Migration (حذف العمود)
        /// </summary>
        public static async Task RollbackMigrationAsync(DbContext context)
        {
            try
            {
                // SQLite لا يدعم حذف الأعمدة مباشرة، لذا نحتاج لإعادة إنشاء الجدول
                System.Diagnostics.Debug.WriteLine("⚠️ SQLite لا يدعم حذف الأعمدة. يرجى إعادة إنشاء قاعدة البيانات إذا لزم الأمر.");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التراجع عن Migration: {ex.Message}");
                throw;
            }
        }
    }
}
