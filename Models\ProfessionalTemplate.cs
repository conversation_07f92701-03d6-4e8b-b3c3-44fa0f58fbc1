using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DriverManagementSystem.Models
{
    /// <summary>
    /// نموذج النماذج الاحترافية
    /// </summary>
    public class ProfessionalTemplate
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Type { get; set; } = string.Empty; // UserTemplate, MessageTemplate, ReportTemplate

        [Required]
        public string Content { get; set; } = string.Empty;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [StringLength(200)]
        public string Category { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public bool IsDefault { get; set; } = false;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [StringLength(100)]
        public string CreatedBy { get; set; } = string.Empty;

        public DateTime? ModifiedDate { get; set; }

        [StringLength(100)]
        public string? ModifiedBy { get; set; }

        [StringLength(1000)]
        public string? Tags { get; set; }

        public int UsageCount { get; set; } = 0;

        public DateTime? LastUsedDate { get; set; }

        /// <summary>
        /// الحصول على نوع النموذج بالعربية
        /// </summary>
        [NotMapped]
        public string TypeDisplayName => Type switch
        {
            "UserTemplate" => "نموذج مستخدم",
            "MessageTemplate" => "قالب رسالة",
            "ReportTemplate" => "قالب تقرير",
            _ => "غير محدد"
        };

        /// <summary>
        /// الحصول على حالة النموذج بالعربية
        /// </summary>
        [NotMapped]
        public string StatusDisplayName => IsActive ? "نشط" : "غير نشط";

        /// <summary>
        /// الحصول على تاريخ الإنشاء مُنسق
        /// </summary>
        [NotMapped]
        public string FormattedCreatedDate => CreatedDate.ToString("yyyy/MM/dd HH:mm");

        /// <summary>
        /// الحصول على تاريخ آخر استخدام مُنسق
        /// </summary>
        [NotMapped]
        public string FormattedLastUsedDate => LastUsedDate?.ToString("yyyy/MM/dd HH:mm") ?? "لم يُستخدم بعد";

        /// <summary>
        /// تحديث تاريخ آخر استخدام
        /// </summary>
        public void UpdateLastUsed()
        {
            LastUsedDate = DateTime.Now;
            UsageCount++;
        }

        /// <summary>
        /// نسخ النموذج
        /// </summary>
        public ProfessionalTemplate Clone()
        {
            return new ProfessionalTemplate
            {
                Name = $"{Name} - نسخة",
                Type = Type,
                Content = Content,
                Description = Description,
                Category = Category,
                IsActive = true,
                IsDefault = false,
                CreatedDate = DateTime.Now,
                CreatedBy = "System",
                Tags = Tags
            };
        }
    }

    /// <summary>
    /// إحصائيات النماذج الاحترافية
    /// </summary>
    public class TemplateStatistics
    {
        public int TotalTemplates { get; set; }
        public int UserTemplates { get; set; }
        public int MessageTemplates { get; set; }
        public int ReportTemplates { get; set; }
        public int ActiveTemplates { get; set; }
        public int InactiveTemplates { get; set; }
        public int DefaultTemplates { get; set; }
        public int CustomTemplates { get; set; }
        public DateTime LastCreated { get; set; }
        public DateTime LastUsed { get; set; }
        public string MostUsedTemplate { get; set; } = string.Empty;
        public int TotalUsage { get; set; }
    }
}
