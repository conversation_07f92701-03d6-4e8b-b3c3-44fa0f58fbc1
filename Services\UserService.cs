using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Security.Cryptography;
using System.Text;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    public interface IUserService
    {
        Task<List<User>> GetAllUsersAsync();
        Task<User?> GetUserByIdAsync(int userId);
        Task<User?> GetUserByUsernameAsync(string username);
        Task<bool> CreateUserAsync(User user, string password);
        Task<bool> UpdateUserAsync(User user);
        Task<bool> DeleteUserAsync(int userId);
        Task<bool> ChangePasswordAsync(int userId, string newPassword);
        Task<List<UserPermission>> GetUserPermissionsAsync(int userId);
        Task<bool> UpdateUserPermissionsAsync(int userId, List<UserPermission> permissions);
        Task<bool> ValidateUserAsync(string username, string password);
        string HashPassword(string password);
        bool VerifyPassword(string password, string hash);
    }

    public class UserService : IUserService
    {
        private readonly ApplicationDbContext _context;

        public UserService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<User>> GetAllUsersAsync()
        {
            return await _context.Users
                .Include(u => u.UserPermissions)
                .OrderBy(u => u.FullName)
                .ToListAsync();
        }

        public async Task<User?> GetUserByIdAsync(int userId)
        {
            return await _context.Users
                .Include(u => u.UserPermissions)
                .FirstOrDefaultAsync(u => u.UserId == userId);
        }

        public async Task<User?> GetUserByUsernameAsync(string username)
        {
            return await _context.Users
                .Include(u => u.UserPermissions)
                .FirstOrDefaultAsync(u => u.Username == username);
        }

        public async Task<bool> CreateUserAsync(User user, string password)
        {
            try
            {
                // Check if username already exists
                var existingUser = await GetUserByUsernameAsync(user.Username);
                if (existingUser != null)
                    return false;

                // Hash password
                user.PasswordHash = HashPassword(password);
                user.CreatedDate = DateTime.Now;

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                // Create default permissions based on role
                await CreateDefaultPermissionsAsync(user.UserId, user.Role);

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateUserAsync(User user)
        {
            try
            {
                _context.Users.Update(user);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteUserAsync(int userId)
        {
            try
            {
                var user = await GetUserByIdAsync(userId);
                if (user == null) return false;

                _context.Users.Remove(user);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ChangePasswordAsync(int userId, string newPassword)
        {
            try
            {
                var user = await GetUserByIdAsync(userId);
                if (user == null) return false;

                user.PasswordHash = HashPassword(newPassword);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<UserPermission>> GetUserPermissionsAsync(int userId)
        {
            return await _context.UserPermissions
                .Where(p => p.UserId == userId)
                .ToListAsync();
        }

        public async Task<bool> UpdateUserPermissionsAsync(int userId, List<UserPermission> permissions)
        {
            try
            {
                // Remove existing permissions
                var existingPermissions = await GetUserPermissionsAsync(userId);
                _context.UserPermissions.RemoveRange(existingPermissions);

                // Add new permissions
                foreach (var permission in permissions)
                {
                    permission.UserId = userId;
                    permission.CreatedDate = DateTime.Now;
                }
                _context.UserPermissions.AddRange(permissions);

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ValidateUserAsync(string username, string password)
        {
            var user = await GetUserByUsernameAsync(username);
            if (user == null || !user.IsActive)
                return false;

            return VerifyPassword(password, user.PasswordHash);
        }

        public string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        public bool VerifyPassword(string password, string hash)
        {
            var hashedPassword = HashPassword(password);
            return hashedPassword == hash;
        }

        private async Task CreateDefaultPermissionsAsync(int userId, string role)
        {
            var permissions = new List<UserPermission>();

            switch (role.ToLower())
            {
                case "admin":
                    permissions = GetAllPermissions(userId);
                    break;
                case "manager":
                    permissions = GetManagerPermissions(userId);
                    break;
                case "user":
                    permissions = GetUserPermissions(userId);
                    break;
                case "viewer":
                    permissions = GetViewerPermissions(userId);
                    break;
            }

            _context.UserPermissions.AddRange(permissions);
            await _context.SaveChangesAsync();
        }

        private List<UserPermission> GetAllPermissions(int userId)
        {
            return new List<UserPermission>
            {
                new UserPermission { UserId = userId, PermissionName = "ViewDashboard", PermissionDescription = "عرض لوحة التحكم", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "ManageDrivers", PermissionDescription = "إدارة السائقين", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "ManageUsers", PermissionDescription = "إدارة المستخدمين", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "ViewReports", PermissionDescription = "عرض التقارير", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "ManageSettings", PermissionDescription = "إدارة الإعدادات", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "ManageContracts", PermissionDescription = "إدارة العقود", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "ManagePricing", PermissionDescription = "إدارة الأسعار", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "SendMessages", PermissionDescription = "إرسال الرسائل", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "ViewRoutes", PermissionDescription = "عرض الطرق", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "ManageDropData", PermissionDescription = "إدارة بيانات النزول", IsGranted = true }
            };
        }

        private List<UserPermission> GetManagerPermissions(int userId)
        {
            return new List<UserPermission>
            {
                new UserPermission { UserId = userId, PermissionName = "ViewDashboard", PermissionDescription = "عرض لوحة التحكم", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "ManageDrivers", PermissionDescription = "إدارة السائقين", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "ViewReports", PermissionDescription = "عرض التقارير", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "ManageContracts", PermissionDescription = "إدارة العقود", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "ManagePricing", PermissionDescription = "إدارة الأسعار", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "SendMessages", PermissionDescription = "إرسال الرسائل", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "ViewRoutes", PermissionDescription = "عرض الطرق", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "ManageDropData", PermissionDescription = "إدارة بيانات النزول", IsGranted = true }
            };
        }

        private List<UserPermission> GetUserPermissions(int userId)
        {
            return new List<UserPermission>
            {
                new UserPermission { UserId = userId, PermissionName = "ViewDashboard", PermissionDescription = "عرض لوحة التحكم", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "ViewReports", PermissionDescription = "عرض التقارير", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "ViewRoutes", PermissionDescription = "عرض الطرق", IsGranted = true }
            };
        }

        private List<UserPermission> GetViewerPermissions(int userId)
        {
            return new List<UserPermission>
            {
                new UserPermission { UserId = userId, PermissionName = "ViewDashboard", PermissionDescription = "عرض لوحة التحكم", IsGranted = true },
                new UserPermission { UserId = userId, PermissionName = "ViewReports", PermissionDescription = "عرض التقارير", IsGranted = true }
            };
        }
    }
}
