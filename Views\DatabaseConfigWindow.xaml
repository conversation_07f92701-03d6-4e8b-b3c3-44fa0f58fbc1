<Window x:Class="DriverManagementSystem.Views.DatabaseConfigWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعداد قاعدة البيانات - SQL Server" 
        Height="650" Width="800"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="#F8F9FA">

    <Window.Resources>
        <Style TargetType="TextBox">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
        </Style>
        
        <Style TargetType="Button">
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>
        
        <Style TargetType="RadioButton">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="10,5"/>
        </Style>
        
        <Style TargetType="CheckBox">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="10,5"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#4A90E2" CornerRadius="10" Padding="20" Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="🗄️" FontSize="24" Margin="0,0,10,0"/>
                <StackPanel>
                    <TextBlock Text="إعداد اتصال قاعدة البيانات" 
                               FontSize="20" FontWeight="Bold" 
                               Foreground="White" HorizontalAlignment="Center"/>
                    <TextBlock Text="SQL Server Database Configuration" 
                               FontSize="14" Foreground="#E3F2FD" 
                               HorizontalAlignment="Center"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- نوع قاعدة البيانات -->
                <GroupBox Header="نوع قاعدة البيانات" FontSize="16" FontWeight="Bold" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <RadioButton x:Name="RadioServerDatabase" Content="قاعدة بيانات خادم (Server Database)" 
                                   IsChecked="True" Checked="RadioServerDatabase_Checked"/>
                        <RadioButton x:Name="RadioLocalDatabase" Content="قاعدة بيانات محلية (Local Database) - قابلة للنقل" 
                                   Checked="RadioLocalDatabase_Checked"/>
                        
                        <Border Background="#E8F5E8" CornerRadius="5" Padding="10" Margin="0,10,0,0">
                            <StackPanel>
                                <TextBlock Text="💡 قاعدة البيانات المحلية:" FontWeight="Bold" Foreground="#2E7D32"/>
                                <TextBlock Text="• يتم حفظها في مجلد Data داخل النظام" Margin="10,5,0,0"/>
                                <TextBlock Text="• يمكن نسخ النظام كاملاً إلى أي جهاز آخر" Margin="10,0,0,0"/>
                                <TextBlock Text="• لا تحتاج إعداد خادم SQL Server منفصل" Margin="10,0,0,0"/>
                                <TextBlock Text="• مناسبة للاستخدام الشخصي أو المكاتب الصغيرة" Margin="10,0,0,5"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </GroupBox>

                <!-- إعدادات الخادم -->
                <GroupBox x:Name="ServerSettingsGroup" Header="إعدادات الخادم" FontSize="16" FontWeight="Bold" Margin="0,0,0,20">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Server Name -->
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم الخادم (Server Name):" 
                                 VerticalAlignment="Center" Margin="0,0,10,10"/>
                        <TextBox x:Name="TxtServerName" Grid.Row="0" Grid.Column="1" 
                               Text="localhost" Margin="0,0,0,10"/>

                        <!-- Database Name -->
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="اسم قاعدة البيانات (Database Name):" 
                                 VerticalAlignment="Center" Margin="0,0,10,10"/>
                        <TextBox x:Name="TxtDatabaseName" Grid.Row="1" Grid.Column="1" 
                               Text="SFDSYS" Margin="0,0,0,10"/>

                        <!-- Authentication -->
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="نوع المصادقة (Authentication):" 
                                 VerticalAlignment="Top" Margin="0,5,10,10"/>
                        <StackPanel Grid.Row="2" Grid.Column="1" Margin="0,0,0,10">
                            <RadioButton x:Name="RadioWindowsAuth" Content="Windows Authentication" 
                                       IsChecked="True" Checked="RadioWindowsAuth_Checked"/>
                            <RadioButton x:Name="RadioSqlAuth" Content="SQL Server Authentication" 
                                       Checked="RadioSqlAuth_Checked"/>
                        </StackPanel>

                        <!-- SQL Authentication Details -->
                        <StackPanel x:Name="SqlAuthPanel" Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" 
                                  Visibility="Collapsed" Margin="0,0,0,10">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="200"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم المستخدم:" 
                                         VerticalAlignment="Center" Margin="0,0,10,10"/>
                                <TextBox x:Name="TxtUsername" Grid.Row="0" Grid.Column="1" 
                                       Text="sa" Margin="0,0,0,10"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="كلمة المرور:" 
                                         VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <PasswordBox x:Name="TxtPassword" Grid.Row="1" Grid.Column="1" 
                                           Padding="10" FontSize="14" BorderBrush="#DDD" 
                                           BorderThickness="1" Background="White"/>
                            </Grid>
                        </StackPanel>
                    </Grid>
                </GroupBox>

                <!-- إعدادات قاعدة البيانات المحلية -->
                <GroupBox x:Name="LocalDatabaseGroup" Header="إعدادات قاعدة البيانات المحلية" 
                        FontSize="16" FontWeight="Bold" Margin="0,0,0,20" Visibility="Collapsed">
                    <StackPanel Margin="10">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم قاعدة البيانات:" 
                                     VerticalAlignment="Center" Margin="0,0,10,10"/>
                            <TextBox x:Name="TxtLocalDatabaseName" Grid.Row="0" Grid.Column="1"
                                   Text="SFDSYS" Margin="0,0,0,10" TextChanged="TxtLocalDatabaseName_TextChanged"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="مسار الحفظ:" 
                                     VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <TextBox x:Name="TxtLocalDatabasePath" Grid.Row="1" Grid.Column="1" 
                                   IsReadOnly="True" Background="#F5F5F5"/>
                        </Grid>
                        
                        <Border Background="#FFF3E0" CornerRadius="5" Padding="10" Margin="0,10,0,0">
                            <StackPanel>
                                <TextBlock Text="📁 مسار قاعدة البيانات المحلية:" FontWeight="Bold" Foreground="#F57C00"/>
                                <TextBlock x:Name="TxtLocalDbFullPath" Text="" Margin="10,5,0,0" FontFamily="Consolas"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </GroupBox>

                <!-- إدارة قواعد البيانات الموجودة -->
                <GroupBox x:Name="ExistingDatabasesGroup" Header="قواعد البيانات الموجودة" FontSize="16" FontWeight="Bold" Margin="0,0,0,20">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="200"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- شرح -->
                        <TextBlock Grid.Row="0" Text="قائمة قواعد البيانات المتاحة في LocalDB:"
                                 Margin="0,0,0,10" FontWeight="Normal"/>

                        <!-- قائمة قواعد البيانات -->
                        <Border Grid.Row="1" BorderBrush="#CCCCCC" BorderThickness="1" Background="White">
                            <ListBox x:Name="ListDatabases" SelectionMode="Single"
                                   ScrollViewer.VerticalScrollBarVisibility="Auto">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <Grid Margin="5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0" Text="{Binding}"
                                                     VerticalAlignment="Center" FontSize="14"/>

                                            <Button Grid.Column="1" Content="🗑️"
                                                  Width="30" Height="25"
                                                  Background="#FFE6E6" BorderBrush="#FF9999"
                                                  ToolTip="حذف قاعدة البيانات"
                                                  Click="BtnDeleteDatabase_Click"
                                                  Tag="{Binding}"/>
                                        </Grid>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </Border>

                        <!-- أزرار الإدارة -->
                        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,10,0,0">
                            <Button x:Name="BtnRefreshDatabases" Content="🔄 تحديث القائمة"
                                  Width="120" Height="35" Margin="0,0,10,0"
                                  Background="#E3F2FD" BorderBrush="#2196F3"
                                  Click="BtnRefreshDatabases_Click"/>

                            <Button x:Name="BtnSelectDatabase" Content="📋 اختيار قاعدة البيانات"
                                  Width="150" Height="35" Margin="0,0,10,0"
                                  Background="#E8F5E8" BorderBrush="#4CAF50"
                                  Click="BtnSelectDatabase_Click"/>
                        </StackPanel>
                    </Grid>
                </GroupBox>

            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="BtnCancel" Content="❌ إلغاء" Background="#F44336" Foreground="White" 
                    Margin="0,0,10,0" Click="BtnCancel_Click"/>
            <Button x:Name="BtnSaveSettings" Content="💾 حفظ الإعدادات" Background="#2196F3" Foreground="White" 
                    Margin="0,0,10,0" Click="BtnSaveSettings_Click"/>
            <Button x:Name="BtnCreateDatabase" Content="🏗️ إنشاء قاعدة البيانات" Background="#4CAF50" Foreground="White" 
                    Margin="0,0,10,0" Click="BtnCreateDatabase_Click"/>
            <Button x:Name="BtnTestConnection" Content="🔍 اختبار الاتصال" Background="#FF9800" Foreground="White" 
                    Click="BtnTestConnection_Click"/>
        </StackPanel>

    </Grid>
</Window>
