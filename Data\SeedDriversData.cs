using System;
using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;
using DriverManagementSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem.Data
{
    /// <summary>
    /// كلاس لإدراج بيانات السائقين الأولية في قاعدة البيانات
    /// </summary>
    public static class SeedDriversData
    {
        /// <summary>
        /// إدراج بيانات السائقين في قاعدة البيانات
        /// </summary>
        public static async Task SeedDriversAsync(ApplicationDbContext context)
        {
            try
            {
                // التحقق من وجود بيانات مسبقاً
                var existingDriversCount = await context.Drivers.CountAsync();
                if (existingDriversCount > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ يوجد {existingDriversCount} سائق في قاعدة البيانات بالفعل");
                    return; // لا تضيف البيانات إذا كانت موجودة
                }

                System.Diagnostics.Debug.WriteLine("🚀 بدء إدراج بيانات السائقين...");

                var drivers = GetDriversData();
                
                foreach (var driver in drivers)
                {
                    context.Drivers.Add(driver);
                    System.Diagnostics.Debug.WriteLine($"✅ تم إضافة السائق: {driver.Name}");
                }

                await context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"🎉 تم إدراج {drivers.Count} سائق بنجاح!");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إدراج بيانات السائقين: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على قائمة بيانات السائقين
        /// </summary>
        private static List<Driver> GetDriversData()
        {
            var drivers = new List<Driver>();

            // إضافة جميع السائقين
            drivers.Add(CreateDriver("إبراهيم عبدالله الطيب الطيري", "17010087881", "مركز-1-ذمار", "29/12/2021", "9-17708", "06/04/2022", "9-18631", "فورتشن", "فضي", "2009", "شخصية", "770137382", "4 أشخاص", "911102"));
            drivers.Add(CreateDriver("اكرم زيد محمد الخربي", "17010350457", "-", "07/09/2024", "-", "07/09/2024", "9-3860", "حافلة", "ابيض", "2004", "شخصية", "778854986", "4 أشخاص", "91184"));
            drivers.Add(CreateDriver("بشير ابراهيم علي الحجي", "17010002740", "مركز-1-ذمار", "11/11/2014", "9-21514", "04/01/2023", "9-29959", "هايلكس", "ابيض", "2013", "شخصية", "771633491", "4 أشخاص", "91165"));
            drivers.Add(CreateDriver("حسين عبده احمد صالح السباعي", "15010165489", "مركز-1-إب", "25/10/2024", "1-51841", "09/02/2022", "7-25759", "برادو", "ابيض", "2011", "شخصية", "773645381", "4 أشخاص", "91194"));
            drivers.Add(CreateDriver("زياد سعيد احمد حسن السماوي", "17010133060", "مركز-1-ذمار", "22/08/2013", "23-136830", "28/09/2024", "1-238922", "هايلكس", "ابيض", "2015", "شخصية", "772023914", "4 أشخاص", "91192"));
            drivers.Add(CreateDriver("زياد عزيز ثابت الجبري", "17010237486", "مركز -1- ذمار", "02/05/2024", "30-24003", "29/10/2024", "3-111753", "هايلكس", "ابيض", "2013", "شخصية", "773831116", "4 أشخاص", "91191"));
            drivers.Add(CreateDriver("شرف يحيى احمد قايد محرم", "17010111517", "مركز-1-ذمار", "28/08/2012", "23-138390", "20/10/2024", "1-239061", "هايلكس", "ابيض", "2013", "شخصية", "771816913", "4 أشخاص", "91193"));
            drivers.Add(CreateDriver("صالح علي حسن العطير", "17010088912", "مركز-1- ذمار", "09/12/2011", "9-24595", "20/05/2024", "9-31733", "هايلكس", "ابيض", "2012", "شخصية", "772096928", "4 أشخاص", "91186"));
            drivers.Add(CreateDriver("عبدالالة محمد صالح السويدي", "17010031800", "مركز -1- ذمار", "29/05/2007", "3-131570", "14/01/2024", "2-87937", "كنتر", "ابيض", "2007", "شخصية", "777343811", "4 أشخاص", "91196"));
            drivers.Add(CreateDriver("عبدالاله محمد صالح السويدي", "17010031800", "مركز -1- ذمار", "29/05/2007", "9-17233", "13/02/2022", "9-18093", "فورتشن", "ابيض", "2009", "شخصية", "777343811", "4 أشخاص", "91161"));
            drivers.Add(CreateDriver("عبداللطيف محمد علي الجبري", "17010217415", "م-1-ذمار", "16/02/2021", "1-42474", "22/02/2025", "6-33933", "هايلكس", "ابيض", "2015", "شخصية", "770241016", "4 أشخاص", "91163"));
            drivers.Add(CreateDriver("عبدالوارث علي احمد الخطيب", "01010699271", "مركز-1-الأمانة", "10/05/2023", "1-61783", "27/02/2024", "3-54313", "فورتشن", "فضي", "2020", "شخصية", "777274037", "6 أشخاص", "91195"));
            drivers.Add(CreateDriver("عبده علي محمد عايض", "17010052992", "مركز-1-ذمار", "10/09/2014", "3-153168", "23/09/2024", "2-224151", "فورتشن", "ابيض", "2013", "شخصية", "771257033", "6 أشخاص", "91162"));
            drivers.Add(CreateDriver("علوان قايد حسين العماد", "-", "-", "20/02/2025", "-", "20/02/2025", "-", "فورتشن", "-", "-", "-", "771158879", "4 أشخاص", "91199"));
            drivers.Add(CreateDriver("علي احمد محمد المعظم", "17010173930", "مركز-1-ذمار", "17/01/2015", "3-8837", "14/01/2024", "3-1648", "فورتشن", "بني", "2008", "شخصية", "773250700", "6 أشخاص", "91164"));
            drivers.Add(CreateDriver("فيصل حميد احمد الطيبي", "17010011554", "مركز-1-ذمار", "12/07/2009", "3-167502", "13/08/2024", "2-233560", "برادو", "ابيض لؤلؤي", "2009", "شخصية", "775597743", "4 أشخاص", "911103"));
            drivers.Add(CreateDriver("مجاهد سنان صالح عبيد", "17010021326", "مركز-1-ذمار", "04/01/2023", "23-99936", "25/06/2023", "1-202822", "برادو", "ابيض", "2012", "شخصية", "775317517", "6 أشخاص", "91168"));
            drivers.Add(CreateDriver("محمد احمد علي يحيى الجعوري", "17010148844", "مركز-1-ذمار", "06/04/2014", "9-17572", "14/08/2023", "9-18748", "فورتشن", "ابيض", "2010", "شخصية", "777673632", "4 أشخاص", "91169"));
            drivers.Add(CreateDriver("محمد عبده علي محمد عايض", "17010121494", "مركز-1-ذمار", "12/03/2013", "9-8096", "01/10/2024", "9-19764", "هايلكس", "أبيض", "2013", "شخصية", "733670852", "4 أشخاص", "91180"));
            drivers.Add(CreateDriver("محمد مسعود محمد الاهمش", "17010117064", "مركز-1-ذمار", "22/12/2012", "9-10423", "03/06/2023", "9-23029", "فورتشن", "فضي", "2009", "شخصية", "777426675", "6 أشخاص", "91170"));
            drivers.Add(CreateDriver("محمود علي احمد ناجي الشامي", "08010094601", "مركز-1-حضرموت", "26/08/2014", "3-30699", "10/04/2023", "2-125722", "هايلكس", "ابيض", "2013", "شخصية", "777712605", "4 أشخاص", "91171"));
            drivers.Add(CreateDriver("مصلح محمد علي محمد الجبري", "17010066770", "مركز-1-ذمار", "18/07/2022", "-", "11/12/2018", "9-14494", "هايلكس", "ابيض", "2011", "شخصية", "777467099", "4 أشخاص", "91172"));
            drivers.Add(CreateDriver("منيف احمد مسعد الفاطمي", "17010016963", "مركز-1-ذمار", "04/11/2022", "31-7859", "09/09/2024", "2-183096", "هايلكس", "ابيض", "2009", "شخصية", "772889972", "4 أشخاص", "91173"));
            drivers.Add(CreateDriver("ناصر احمد صالح شملي", "17010056708", "مركز-1-ذمار", "20/04/2014", "9-16000", "23/07/2024", "9-17259", "فورتشن", "ابيض", "2012", "شخصية", "770844427", "6 أشخاص", "91174"));
            drivers.Add(CreateDriver("نبيل سعيد محمد الشامي", "17010097498", "مركز-1- ذمار", "28/08/2024", "3-45095", "10/09/2024", "2-135669", "هايلكس", "ابيض", "2013", "شخصية", "777842592", "4 أشخاص", "91187"));
            drivers.Add(CreateDriver("نصر العزي مصلح اللواع", "17010122095", "مركز - 1 - ذمار", "03/10/2023", "23-25386", "18/07/2022", "1-103556", "هايلكس", "ابيض", "2012", "شخصية", "770866403", "4 أشخاص", "91175"));
            drivers.Add(CreateDriver("نصر ناجي قايد السماوي", "17010165982", "-", "", "-", "", "19725-9", "هايلكس", "ابيض", "2012", "شخصية", "773310870", "4 أشخاص", "91176"));
            drivers.Add(CreateDriver("نعمان يحيى علي السماوي", "17010115567", "مركز -1-ذمار", "08/09/2024", "9-17396", "27/02/2022", "9-18153", "فورتشن", "بني", "2018", "شخصية", "771811122", "4 أشخاص", "91188"));
            drivers.Add(CreateDriver("هارون عبدالولي احمد علي العفيري", "17010176874", "مركز-1-ذمار", "26/12/2021", "9-21095", "12/12/2022", "9-29642", "فورتشن", "ابيض", "2009", "شخصية", "777851492", "4 أشخاص", "91177"));
            drivers.Add(CreateDriver("يحيى محمد يحيى قريمه", "17010361302", "-", "", "-", "", "9/77", "حافلة", "ابيض", "2005", "شخصية", "770307080", "4 أشخاص", "91185"));
            drivers.Add(CreateDriver("يوسف احمد علي يحيى الجعوري", "17010088326", "مركز-1-ذمار", "03/02/2011", "3-15454", "11/09/2024", "2-124311", "برادو", "ابيض", "2011", "شخصية", "770277059", "4 أشخاص", "91178"));

            return drivers;
        }

        /// <summary>
        /// إنشاء كائن سائق من البيانات
        /// </summary>
        private static Driver CreateDriver(string name, string cardNumber, string cardIssuePlace, string cardIssueDate, 
            string licenseNumber, string licenseIssueDate, string vehicleNumber, string vehicleType, string vehicleColor, 
            string vehicleModel, string cardType, string phoneNumber, string vehicleCapacity, string driverCode)
        {
            return new Driver
            {
                // البيانات الشخصية
                Name = name,
                PhoneNumber = phoneNumber,
                CardNumber = cardNumber == "-" ? "" : cardNumber,
                CardType = cardType == "-" ? "بطاقة شخصية" : cardType,
                CardIssuePlace = cardIssuePlace == "-" ? "" : cardIssuePlace,
                CardIssueDate = ParseDate(cardIssueDate),
                
                // بيانات الرخصة
                LicenseNumber = licenseNumber == "-" ? "" : licenseNumber,
                LicenseIssueDate = ParseDate(licenseIssueDate),
                
                // بيانات السيارة
                VehicleNumber = vehicleNumber == "-" ? "" : vehicleNumber,
                VehicleType = vehicleType,
                VehicleColor = vehicleColor == "-" ? "" : vehicleColor,
                VehicleModel = vehicleModel == "-" ? "" : vehicleModel,
                VehicleCapacity = vehicleCapacity,
                
                // بيانات النظام
                DriverCode = driverCode,
                SectorId = 1, // القطاع الافتراضي
                SectorName = "القطاع العام",
                IsActive = true,
                CreatedAt = DateTime.Now,
                Notes = "تم إدراجه من البيانات الأولية"
            };
        }

        /// <summary>
        /// تحويل النص إلى تاريخ
        /// </summary>
        private static DateTime ParseDate(string dateString)
        {
            if (string.IsNullOrWhiteSpace(dateString) || dateString == "-")
                return DateTime.Now;

            try
            {
                // محاولة تحويل التاريخ بصيغة dd/MM/yyyy
                if (DateTime.TryParseExact(dateString, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime result))
                {
                    return result;
                }
                
                // محاولة تحويل التاريخ بصيغ أخرى
                if (DateTime.TryParse(dateString, out DateTime result2))
                {
                    return result2;
                }
            }
            catch
            {
                // في حالة فشل التحويل
            }

            return DateTime.Now; // تاريخ افتراضي
        }
    }
}
