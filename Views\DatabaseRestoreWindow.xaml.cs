using System;
using System.Threading.Tasks;
using System.Windows;
using DriverManagementSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// Interaction logic for DatabaseRestoreWindow.xaml
    /// </summary>
    public partial class DatabaseRestoreWindow : Window
    {
        public DatabaseRestoreWindow()
        {
            InitializeComponent();
            LoadDatabaseInfoAsync();
        }

        /// <summary>
        /// تحميل معلومات قاعدة البيانات
        /// </summary>
        private async Task LoadDatabaseInfoAsync()
        {
            try
            {
                LogMessage("🔄 جاري تحميل معلومات قاعدة البيانات...");

                using var context = new ApplicationDbContext();
                
                // التحقق من الاتصال
                var canConnect = await context.Database.CanConnectAsync();
                if (!canConnect)
                {
                    LogMessage("❌ لا يمكن الاتصال بقاعدة البيانات");
                    return;
                }

                // تحميل الإحصائيات
                var visitsCount = await context.FieldVisits.CountAsync();
                var driversCount = await context.Drivers.CountAsync();
                var projectsCount = await context.Projects.CountAsync();
                var sectorsCount = await context.Sectors.CountAsync();
                var officersCount = await context.Officers.CountAsync();
                var usersCount = await context.Users.CountAsync();

                // تحديث الواجهة
                VisitsCountText.Text = visitsCount.ToString();
                DriversCountText.Text = driversCount.ToString();
                ProjectsCountText.Text = projectsCount.ToString();
                SectorsCountText.Text = sectorsCount.ToString();
                OfficersCountText.Text = officersCount.ToString();
                UsersCountText.Text = usersCount.ToString();

                LogMessage($"✅ تم تحميل المعلومات - الزيارات: {visitsCount}, السائقين: {driversCount}, المشاريع: {projectsCount}");
            }
            catch (Exception ex)
            {
                LogMessage($"❌ خطأ في تحميل معلومات قاعدة البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث المعلومات
        /// </summary>
        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            RefreshButton.IsEnabled = false;
            try
            {
                await LoadDatabaseInfoAsync();
            }
            finally
            {
                RefreshButton.IsEnabled = true;
            }
        }

        /// <summary>
        /// استعادة البيانات
        /// </summary>
        private async void RestoreButton_Click(object sender, RoutedEventArgs e)
        {
            RestoreButton.IsEnabled = false;
            try
            {
                LogMessage("🔄 بدء استعادة البيانات...");
                
                var result = await DatabaseRestorer.RestoreDatabaseAsync();
                
                if (result)
                {
                    LogMessage("✅ تم استعادة البيانات بنجاح");
                    await LoadDatabaseInfoAsync();
                    MessageBox.Show("تم استعادة البيانات بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    LogMessage("❌ فشل في استعادة البيانات");
                    MessageBox.Show("فشل في استعادة البيانات. تحقق من سجل العمليات للتفاصيل.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"❌ خطأ في استعادة البيانات: {ex.Message}");
                MessageBox.Show($"خطأ في استعادة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                RestoreButton.IsEnabled = true;
            }
        }

        /// <summary>
        /// إعادة تعيين قاعدة البيانات
        /// </summary>
        private async void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من إعادة تعيين قاعدة البيانات؟\n\nسيتم حذف جميع البيانات الموجودة واستعادة البيانات التجريبية فقط.\n\nهذه العملية لا يمكن التراجع عنها!",
                "تأكيد إعادة التعيين",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result != MessageBoxResult.Yes)
                return;

            ResetButton.IsEnabled = false;
            try
            {
                LogMessage("🔄 بدء إعادة تعيين قاعدة البيانات...");
                
                var resetResult = await DatabaseRestorer.ResetDatabaseAsync();
                
                if (resetResult)
                {
                    LogMessage("✅ تم إعادة تعيين قاعدة البيانات بنجاح");
                    await LoadDatabaseInfoAsync();
                    MessageBox.Show("تم إعادة تعيين قاعدة البيانات بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    LogMessage("❌ فشل في إعادة تعيين قاعدة البيانات");
                    MessageBox.Show("فشل في إعادة تعيين قاعدة البيانات. تحقق من سجل العمليات للتفاصيل.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"❌ خطأ في إعادة تعيين قاعدة البيانات: {ex.Message}");
                MessageBox.Show($"خطأ في إعادة تعيين قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ResetButton.IsEnabled = true;
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// إضافة رسالة إلى سجل العمليات
        /// </summary>
        private void LogMessage(string message)
        {
            Dispatcher.Invoke(() =>
            {
                var timestamp = DateTime.Now.ToString("HH:mm:ss");
                LogTextBox.AppendText($"[{timestamp}] {message}\n");
                LogTextBox.ScrollToEnd();
            });
        }
    }
}
