using System;
using System.Globalization;

namespace DriverManagementSystem.Services
{
    public static class HijriDateService
    {
        private static readonly UmAlQuraCalendar hijriCalendar = new UmAlQuraCalendar();
        
        public static string ConvertToHijri(DateTime gregorianDate)
        {
            try
            {
                int hijriYear = hijriCalendar.GetYear(gregorianDate);
                int hijriMonth = hijriCalendar.GetMonth(gregorianDate);
                int hijriDay = hijriCalendar.GetDayOfMonth(gregorianDate);
                
                string monthName = GetHijriMonthName(hijriMonth);
                
                return $"{hijriDay:D2}/{monthName}/{hijriYear}هـ";
            }
            catch
            {
                return "غير محدد";
            }
        }
        
        private static string GetHijriMonthName(int month)
        {
            return month switch
            {
                1 => "محرم",
                2 => "صفر",
                3 => "ربيع الأول",
                4 => "ربيع الثاني",
                5 => "جمادى الأولى",
                6 => "جمادى الثانية",
                7 => "رجب",
                8 => "شعبان",
                9 => "رمضان",
                10 => "شوال",
                11 => "ذو القعدة",
                12 => "ذو الحجة",
                _ => "غير معروف"
            };
        }
        
        public static int CalculateDaysBetween(DateTime startDate, DateTime endDate)
        {
            if (endDate < startDate)
                return 0;

            // تحويل التواريخ إلى تواريخ فقط بدون وقت لضمان الحساب الصحيح
            var startDateOnly = startDate.Date;
            var endDateOnly = endDate.Date;

            // حساب الفرق بالأيام + 1 لتضمين يوم البداية
            var daysDifference = (endDateOnly - startDateOnly).Days + 1;

            System.Diagnostics.Debug.WriteLine($"🔢 حساب الأيام: من {startDateOnly:dd/MM/yyyy} إلى {endDateOnly:dd/MM/yyyy} = {daysDifference} أيام");

            return daysDifference;
        }
    }
}
