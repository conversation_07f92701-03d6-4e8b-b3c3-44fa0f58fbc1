<Window x:Class="DriverManagementSystem.Views.InitialSetupWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="🚀 إعداد النظام الأولي - SFD System"
        Height="600" Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent">

    <Window.Resources>
        <!-- تدرج الخلفية الرئيسي -->
        <LinearGradientBrush x:Key="MainGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#667eea" Offset="0"/>
            <GradientStop Color="#764ba2" Offset="1"/>
        </LinearGradientBrush>

        <!-- تدرج الأزرار -->
        <LinearGradientBrush x:Key="ButtonGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#4CAF50" Offset="0"/>
            <GradientStop Color="#45a049" Offset="1"/>
        </LinearGradientBrush>

        <!-- ستايل النصوص -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="28"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,20,0,10"/>
        </Style>

        <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#E0E0E0"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,30"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>

        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>

        <!-- ستايل مربعات النص -->
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="2"
                                BorderBrush="Transparent">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Padding="{TemplateBinding Padding}"
                                        VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter TargetName="PART_ContentHost" Property="BorderBrush" Value="#4CAF50"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- ستايل مربع كلمة المرور -->
        <Style x:Key="ModernPasswordBoxStyle" TargetType="PasswordBox">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="PasswordBox">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="2"
                                BorderBrush="Transparent">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Padding="{TemplateBinding Padding}"
                                        VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter TargetName="PART_ContentHost" Property="BorderBrush" Value="#4CAF50"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- ستايل الأزرار -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Background" Value="{StaticResource ButtonGradient}"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="30,12"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="25"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                            <GradientStop Color="#5CBF60" Offset="0"/>
                                            <GradientStop Color="#4CAF50" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                            <GradientStop Color="#45a049" Offset="0"/>
                                            <GradientStop Color="#3d8b40" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <!-- الحاوية الرئيسية -->
    <Border Background="{StaticResource MainGradient}" CornerRadius="15">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="315" ShadowDepth="10" Opacity="0.3" BlurRadius="15"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- شريط العنوان -->
            <Grid Grid.Row="0" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="🚀 إعداد النظام الأولي" 
                          FontFamily="Segoe UI" FontSize="16" FontWeight="SemiBold"
                          Foreground="White" Margin="20,15" VerticalAlignment="Center"/>

                <Button Grid.Column="1" Content="✕" Width="40" Height="40" 
                       Background="Transparent" BorderThickness="0" 
                       Foreground="White" FontSize="16" FontWeight="Bold"
                       Click="CloseButton_Click" Cursor="Hand"
                       Margin="0,10,10,0"/>
            </Grid>

            <!-- المحتوى الرئيسي -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="40,20">
                <StackPanel>
                    <!-- الأيقونة والعنوان -->
                    <TextBlock Text="🔐" FontSize="60" HorizontalAlignment="Center" Margin="0,0,0,20"/>
                    
                    <TextBlock Text="مرحباً بك في نظام إدارة السائقين" Style="{StaticResource HeaderTextStyle}"/>
                    
                    <TextBlock Style="{StaticResource SubHeaderTextStyle}">
                        <Run Text="يرجى إنشاء حساب المدير الرئيسي للنظام"/>
                        <LineBreak/>
                        <Run Text="هذا الحساب سيكون له صلاحيات كاملة لإدارة النظام"/>
                    </TextBlock>

                    <!-- نموذج البيانات -->
                    <StackPanel Margin="0,20,0,0">
                        <!-- اسم المستخدم -->
                        <TextBlock Text="👤 اسم المستخدم:" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="UsernameTextBox" 
                                Style="{StaticResource ModernTextBoxStyle}"
                                Text="admin"
                                ToolTip="أدخل اسم المستخدم (يُفضل admin)"/>

                        <!-- الاسم الكامل -->
                        <TextBlock Text="📝 الاسم الكامل:" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="FullNameTextBox" 
                                Style="{StaticResource ModernTextBoxStyle}"
                                Text="المسئول الرئيسي"
                                ToolTip="أدخل الاسم الكامل للمدير"/>

                        <!-- البريد الإلكتروني -->
                        <TextBlock Text="📧 البريد الإلكتروني:" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="EmailTextBox" 
                                Style="{StaticResource ModernTextBoxStyle}"
                                Text="<EMAIL>"
                                ToolTip="أدخل البريد الإلكتروني"/>

                        <!-- كلمة المرور -->
                        <TextBlock Text="🔒 كلمة المرور:" Style="{StaticResource LabelStyle}"/>
                        <PasswordBox x:Name="PasswordBox" 
                                    Style="{StaticResource ModernPasswordBoxStyle}"
                                    ToolTip="أدخل كلمة مرور قوية (8 أحرف على الأقل)"/>

                        <!-- تأكيد كلمة المرور -->
                        <TextBlock Text="🔒 تأكيد كلمة المرور:" Style="{StaticResource LabelStyle}"/>
                        <PasswordBox x:Name="ConfirmPasswordBox" 
                                    Style="{StaticResource ModernPasswordBoxStyle}"
                                    ToolTip="أعد إدخال كلمة المرور للتأكيد"/>
                    </StackPanel>

                    <!-- رسالة الحالة -->
                    <TextBlock x:Name="StatusMessage" 
                              FontFamily="Segoe UI" FontSize="12"
                              Foreground="#FFE0E0" 
                              HorizontalAlignment="Center"
                              Margin="0,10,0,0"
                              TextWrapping="Wrap"/>
                </StackPanel>
            </ScrollViewer>

            <!-- الأزرار -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,30">
                <Button Content="🚀 إنشاء الحساب وبدء النظام" 
                       Style="{StaticResource ModernButtonStyle}"
                       Click="CreateAccount_Click"
                       x:Name="CreateButton"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>
