<Window x:Class="DriverManagementSystem.Views.ProfessionalPrintPreviewWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="معاينة الطباعة - تقرير الزيارة الميدانية" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="#F5F5F5">

    <Window.Resources>
        <!-- أنماط الأزرار الاحترافية -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="2">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E3F2FD"/>
                                <Setter Property="BorderBrush" Value="#2196F3"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#BBDEFB"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط ComboBox احترافي -->
        <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Height" Value="28"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- شريط الأدوات العلوي -->
            <RowDefinition Height="Auto"/> <!-- شريط الإعدادات -->
            <RowDefinition Height="*"/>    <!-- منطقة المعاينة -->
            <RowDefinition Height="Auto"/> <!-- شريط الحالة -->
        </Grid.RowDefinitions>

        <!-- شريط الأدوات العلوي مثل Microsoft Office -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal" Margin="10,8">
                
                <!-- مجموعة الطباعة -->
                <StackPanel Orientation="Horizontal" Margin="0,0,20,0">
                    <Button Content="🖨️ طباعة" Style="{StaticResource ModernButtonStyle}" 
                            Background="#0078D4" Foreground="White" FontWeight="Bold"
                            Width="80" Click="PrintButton_Click"/>
                    <Button Content="⚙️" Style="{StaticResource ModernButtonStyle}" 
                            ToolTip="إعدادات الطباعة" Click="PrintSettingsButton_Click"/>
                </StackPanel>

                <!-- فاصل -->
                <Border Width="1" Height="20" Background="#E0E0E0" Margin="5,0"/>

                <!-- مجموعة التكبير -->
                <StackPanel Orientation="Horizontal" Margin="20,0">
                    <TextBlock Text="التكبير:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <Button Content="🔍-" Style="{StaticResource ModernButtonStyle}" 
                            Width="30" Click="ZoomOutButton_Click"/>
                    <ComboBox x:Name="ZoomComboBox" Style="{StaticResource ModernComboBoxStyle}"
                              Width="80" SelectionChanged="ZoomComboBox_SelectionChanged">
                        <ComboBoxItem Content="50%"/>
                        <ComboBoxItem Content="75%" IsSelected="True"/>
                        <ComboBoxItem Content="100%"/>
                        <ComboBoxItem Content="125%"/>
                        <ComboBoxItem Content="150%"/>
                        <ComboBoxItem Content="200%"/>
                        <ComboBoxItem Content="ملء الصفحة"/>
                        <ComboBoxItem Content="ملء العرض"/>
                    </ComboBox>
                    <Button Content="🔍+" Style="{StaticResource ModernButtonStyle}" 
                            Width="30" Click="ZoomInButton_Click"/>
                </StackPanel>

                <!-- فاصل -->
                <Border Width="1" Height="20" Background="#E0E0E0" Margin="5,0"/>

                <!-- مجموعة العرض -->
                <StackPanel Orientation="Horizontal" Margin="20,0">
                    <Button Content="📄 صفحة واحدة" Style="{StaticResource ModernButtonStyle}" 
                            Click="SinglePageView_Click"/>
                    <Button Content="📄📄 صفحتان" Style="{StaticResource ModernButtonStyle}" 
                            Click="TwoPageView_Click"/>
                    <Button Content="📋 عدة صفحات" Style="{StaticResource ModernButtonStyle}" 
                            Click="MultiPageView_Click"/>
                </StackPanel>

                <!-- فاصل -->
                <Border Width="1" Height="20" Background="#E0E0E0" Margin="5,0"/>

                <!-- مجموعة التنقل -->
                <StackPanel Orientation="Horizontal" Margin="20,0">
                    <Button Content="⏮️" Style="{StaticResource ModernButtonStyle}" 
                            ToolTip="الصفحة الأولى" Click="FirstPageButton_Click"/>
                    <Button Content="⏪" Style="{StaticResource ModernButtonStyle}" 
                            ToolTip="الصفحة السابقة" Click="PreviousPageButton_Click"/>
                    <TextBox x:Name="CurrentPageTextBox" Width="40" Height="26" 
                             TextAlignment="Center" VerticalContentAlignment="Center"
                             Text="1" TextChanged="CurrentPageTextBox_TextChanged"/>
                    <TextBlock x:Name="TotalPagesTextBlock" Text="من 5" 
                               VerticalAlignment="Center" Margin="5,0"/>
                    <Button Content="⏩" Style="{StaticResource ModernButtonStyle}" 
                            ToolTip="الصفحة التالية" Click="NextPageButton_Click"/>
                    <Button Content="⏭️" Style="{StaticResource ModernButtonStyle}" 
                            ToolTip="الصفحة الأخيرة" Click="LastPageButton_Click"/>
                </StackPanel>

                <!-- فاصل -->
                <Border Width="1" Height="20" Background="#E0E0E0" Margin="5,0"/>

                <!-- إغلاق -->
                <Button Content="❌ إغلاق" Style="{StaticResource ModernButtonStyle}" 
                        Margin="20,0,0,0" Click="CloseButton_Click"/>
            </StackPanel>
        </Border>

        <!-- شريط الإعدادات الثانوي -->
        <Border Grid.Row="1" Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal" Margin="10,6">
                
                <!-- اختيار الطابعة -->
                <StackPanel Orientation="Horizontal" Margin="0,0,30,0">
                    <TextBlock Text="الطابعة:" VerticalAlignment="Center" Margin="0,0,8,0" FontWeight="Bold"/>
                    <ComboBox x:Name="PrinterComboBox" Style="{StaticResource ModernComboBoxStyle}"
                              Width="200" SelectionChanged="PrinterComboBox_SelectionChanged"/>
                    <Button Content="🔄" Style="{StaticResource ModernButtonStyle}" 
                            ToolTip="تحديث قائمة الطابعات" Width="28" Click="RefreshPrintersButton_Click"/>
                </StackPanel>

                <!-- حجم الورق -->
                <StackPanel Orientation="Horizontal" Margin="0,0,30,0">
                    <TextBlock Text="حجم الورق:" VerticalAlignment="Center" Margin="0,0,8,0" FontWeight="Bold"/>
                    <ComboBox x:Name="PaperSizeComboBox" Style="{StaticResource ModernComboBoxStyle}"
                              Width="120" SelectionChanged="PaperSizeComboBox_SelectionChanged">
                        <ComboBoxItem Content="A4" IsSelected="True"/>
                        <ComboBoxItem Content="A3"/>
                        <ComboBoxItem Content="Letter"/>
                        <ComboBoxItem Content="Legal"/>
                    </ComboBox>
                </StackPanel>

                <!-- اتجاه الصفحة -->
                <StackPanel Orientation="Horizontal" Margin="0,0,30,0">
                    <TextBlock Text="الاتجاه:" VerticalAlignment="Center" Margin="0,0,8,0" FontWeight="Bold"/>
                    <ComboBox x:Name="OrientationComboBox" Style="{StaticResource ModernComboBoxStyle}"
                              Width="100" SelectionChanged="OrientationComboBox_SelectionChanged">
                        <ComboBoxItem Content="عمودي" IsSelected="True"/>
                        <ComboBoxItem Content="أفقي"/>
                    </ComboBox>
                </StackPanel>

                <!-- نطاق الصفحات -->
                <StackPanel Orientation="Horizontal" Margin="0,0,30,0">
                    <TextBlock Text="الصفحات:" VerticalAlignment="Center" Margin="0,0,8,0" FontWeight="Bold"/>
                    <ComboBox x:Name="PageRangeComboBox" Style="{StaticResource ModernComboBoxStyle}"
                              Width="120" SelectionChanged="PageRangeComboBox_SelectionChanged">
                        <ComboBoxItem Content="جميع الصفحات" IsSelected="True"/>
                        <ComboBoxItem Content="الصفحة الحالية"/>
                        <ComboBoxItem Content="نطاق مخصص"/>
                    </ComboBox>
                    <TextBox x:Name="CustomRangeTextBox" Style="{x:Null}"
                             Width="80" Height="26" Margin="5,0,0,0"
                             VerticalContentAlignment="Center"
                             Text="1-5" IsEnabled="False"/>
                </StackPanel>

                <!-- عدد النسخ -->
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="النسخ:" VerticalAlignment="Center" Margin="0,0,8,0" FontWeight="Bold"/>
                    <TextBox x:Name="CopiesTextBox" Width="50" Height="26"
                             TextAlignment="Center" VerticalContentAlignment="Center"
                             Text="1"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- منطقة المعاينة الرئيسية -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/> <!-- شريط جانبي للصفحات المصغرة -->
                <ColumnDefinition Width="5"/>   <!-- فاصل -->
                <ColumnDefinition Width="*"/>   <!-- منطقة المعاينة الرئيسية -->
            </Grid.ColumnDefinitions>

            <!-- شريط الصفحات المصغرة -->
            <Border Grid.Column="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,1,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Text="الصفحات" FontWeight="Bold" 
                               Margin="10,10,10,5" FontSize="12"/>
                    
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel x:Name="ThumbnailsPanel" Margin="5"/>
                    </ScrollViewer>
                </Grid>
            </Border>

            <!-- فاصل قابل للسحب -->
            <GridSplitter Grid.Column="1" Width="5" Background="#E0E0E0" 
                          HorizontalAlignment="Stretch" VerticalAlignment="Stretch"/>

            <!-- منطقة المعاينة الرئيسية -->
            <Border Grid.Column="2" Background="#E5E5E5">
                <ScrollViewer x:Name="MainPreviewScrollViewer"
                              HorizontalScrollBarVisibility="Auto"
                              VerticalScrollBarVisibility="Auto"
                              Background="#E5E5E5">
                    <StackPanel x:Name="MainPreviewPanel" 
                                HorizontalAlignment="Center"
                                VerticalAlignment="Top"
                                Margin="20"/>
                </ScrollViewer>
            </Border>
        </Grid>

        <!-- شريط الحالة -->
        <Border Grid.Row="3" Background="#F0F0F0" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <Grid Margin="10,4">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock x:Name="StatusTextBlock" Grid.Column="0" 
                           Text="جاهز للطباعة" VerticalAlignment="Center"/>
                
                <TextBlock x:Name="PageInfoTextBlock" Grid.Column="1" 
                           Text="صفحة 1 من 5" VerticalAlignment="Center" Margin="20,0"/>
                
                <TextBlock x:Name="ZoomLevelTextBlock" Grid.Column="2" 
                           Text="75%" VerticalAlignment="Center"/>
            </Grid>
        </Border>
    </Grid>
</Window>
