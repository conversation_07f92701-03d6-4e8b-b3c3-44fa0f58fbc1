using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Models;
using System.Linq;
using System.Collections.Generic;
using System.Text.Json;

namespace DriverManagementSystem.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext() { }

        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options) { }
        public DbSet<User> Users { get; set; }
        public DbSet<UserPermission> UserPermissions { get; set; }
        public DbSet<FieldVisit> FieldVisits { get; set; }
        public DbSet<FieldVisitor> FieldVisitors { get; set; }
        /// <summary>
        /// جدول خط السير اليومي للزيارات الميدانية - تصميم احترافي
        /// </summary>
        public DbSet<FieldVisitItinerary> FieldVisitItineraries { get; set; }
        public DbSet<Sector> Sectors { get; set; }
        public DbSet<Officer> Officers { get; set; }
        public DbSet<Driver> Drivers { get; set; }
        public DbSet<Vehicle> Vehicles { get; set; }
        public DbSet<Project> Projects { get; set; }
        public DbSet<FieldVisitProject> FieldVisitProjects { get; set; }
        public DbSet<DriverQuote> DriverQuotes { get; set; }
        public DbSet<ProfessionalTemplate> ProfessionalTemplates { get; set; }
        public DbSet<ContractTemplate> ContractTemplates { get; set; }
        public DbSet<MessageDocumentation> MessageDocumentations { get; set; }
        public DbSet<MessageAttachment> MessageAttachments { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            try
            {
                var connectionString = DatabaseConfig.GetConnectionString();
                optionsBuilder.UseSqlServer(connectionString);
                System.Diagnostics.Debug.WriteLine($"🗄️ SQL Server Connection: {connectionString.Replace("Password=", "Password=***")}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعداد قاعدة البيانات: {ex.Message}");
                throw;
            }
        }



        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure User entity
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.UserId);
                entity.Property(e => e.Username).IsRequired().HasMaxLength(50);
                entity.Property(e => e.FullName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(100);
                entity.Property(e => e.PasswordHash).IsRequired().HasMaxLength(255);
                entity.Property(e => e.Role).IsRequired().HasMaxLength(20).HasDefaultValue("User");
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETDATE()");

                // Create unique index on Username
                entity.HasIndex(e => e.Username).IsUnique();
                entity.HasIndex(e => e.Email).IsUnique();
            });

            // Configure UserPermission entity
            modelBuilder.Entity<UserPermission>(entity =>
            {
                entity.HasKey(e => e.PermissionId);
                entity.Property(e => e.PermissionName).IsRequired().HasMaxLength(50);
                entity.Property(e => e.PermissionDescription).IsRequired().HasMaxLength(100);
                entity.Property(e => e.IsGranted).HasDefaultValue(false);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETDATE()");

                // Configure relationship
                entity.HasOne(e => e.User)
                      .WithMany(u => u.UserPermissions)
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure Sector entity
            modelBuilder.Entity<Sector>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
            });

            // Configure Officer entity
            modelBuilder.Entity<Officer>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Rank).HasMaxLength(50);
                entity.Property(e => e.Code).IsRequired().HasMaxLength(20);
                entity.Property(e => e.SectorId).IsRequired();

                // Configure relationship
                entity.HasOne<Sector>()
                      .WithMany()
                      .HasForeignKey(e => e.SectorId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure Driver entity
            modelBuilder.Entity<Driver>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.PhoneNumber).HasMaxLength(20);
                entity.Property(e => e.DriverCode).IsRequired().HasMaxLength(20);
                entity.Property(e => e.VehicleNumber).HasMaxLength(20);
                entity.Property(e => e.VehicleType).HasMaxLength(50);
                entity.Property(e => e.CardNumber).HasMaxLength(30);
                entity.Property(e => e.CardType).HasMaxLength(20);
                entity.Property(e => e.LicenseNumber).HasMaxLength(20);
                entity.Property(e => e.VehicleModel).HasMaxLength(50);
                entity.Property(e => e.VehicleCapacity).HasMaxLength(20);
                entity.Property(e => e.VehicleColor).HasMaxLength(20);
                entity.Property(e => e.Notes).HasMaxLength(500);
                entity.Property(e => e.SectorName).HasMaxLength(100);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETDATE()");

                // حقول الأسعار والعروض
                entity.Property(e => e.QuotedPrice).HasColumnType("decimal(18,2)");
                entity.Property(e => e.QuotedDays);
                entity.Property(e => e.QuoteDate);
                entity.Property(e => e.QuoteNotes).HasMaxLength(1000);
                entity.Property(e => e.HasQuote).HasDefaultValue(false);

                // إضافة خاصية IsSelected للواجهة (لا تحفظ في قاعدة البيانات)
                entity.Ignore(e => e.IsSelected);

                // Create unique index on DriverCode
                entity.HasIndex(e => e.DriverCode).IsUnique();
            });

            // Configure Vehicle entity
            modelBuilder.Entity<Vehicle>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.OwnerName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.IdCardNumber).IsRequired().HasMaxLength(20);
                entity.Property(e => e.LicenseNumber).IsRequired().HasMaxLength(20);
                entity.Property(e => e.VehicleType).IsRequired().HasMaxLength(50);
                entity.Property(e => e.PlateNumber).IsRequired().HasMaxLength(20);
                entity.Property(e => e.DriverCode).IsRequired().HasMaxLength(20);
            });

            // Configure Project entity
            modelBuilder.Entity<Project>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ProjectNumber).IsRequired().HasMaxLength(20);
                entity.Property(e => e.ProjectName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETDATE()");

                // Create unique index on ProjectNumber
                entity.HasIndex(e => e.ProjectNumber).IsUnique();
            });

            // Configure FieldVisitProject entity
            modelBuilder.Entity<FieldVisitProject>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ProjectNumber).HasMaxLength(50);
                entity.Property(e => e.ProjectName).HasMaxLength(500);
                entity.Property(e => e.Notes).HasMaxLength(1000);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETDATE()");

                // Create index for performance (removed unique constraint to allow null ProjectId)
                entity.HasIndex(e => e.FieldVisitId);
                entity.HasIndex(e => e.ProjectId);

                // Configure relationships
                entity.HasOne(e => e.FieldVisit)
                      .WithMany()
                      .HasForeignKey(e => e.FieldVisitId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Project)
                      .WithMany()
                      .HasForeignKey(e => e.ProjectId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure FieldVisit entity
            modelBuilder.Entity<FieldVisit>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.VisitNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.AddDate).IsRequired();
                entity.Property(e => e.HijriDate).HasMaxLength(100);
                entity.Property(e => e.DepartureDate).IsRequired();
                entity.Property(e => e.ReturnDate).IsRequired();
                entity.Property(e => e.DaysCount).IsRequired();
                entity.Property(e => e.MissionPurpose).IsRequired().HasMaxLength(1000);
                entity.Property(e => e.SectorId).IsRequired();
                entity.Property(e => e.SectorName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.VisitorsCount).IsRequired();
                entity.Property(e => e.SelectedDrivers).HasMaxLength(2000); // العمود الجديد للسائقين المحددين
                entity.Property(e => e.WinnerDriverMessage).HasMaxLength(2000); // نص الرسالة للسائق الفائز
                entity.Property(e => e.SecurityRoute).HasMaxLength(2000); // خط السير للنقاط الأمنية
                entity.Property(e => e.VisitNotes).HasMaxLength(1000); // ملاحظات إضافية

                // الحقول الجديدة من Excel
                entity.Property(e => e.ApprovalBy).HasMaxLength(100); // الموافقة على السفر
                entity.Property(e => e.SubmissionTime); // وقت وتاريخ الإرسال
                entity.Property(e => e.OdkVisitNumber).HasMaxLength(100); // رقم الزيارة ODK

                // خط السير سيتم حفظه في جدول منفصل
                entity.Ignore(e => e.Itinerary);

                // Create unique index on VisitNumber
                entity.HasIndex(e => e.VisitNumber).IsUnique();

                // Configure relationship with FieldVisitors
                entity.HasMany(e => e.Visitors)
                      .WithOne()
                      .HasForeignKey(v => v.FieldVisitId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Configure relationship with Projects through FieldVisitProject
                entity.HasMany(e => e.Projects)
                      .WithOne(p => p.FieldVisit)
                      .HasForeignKey(p => p.FieldVisitId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure FieldVisitor entity
            modelBuilder.Entity<FieldVisitor>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FieldVisitId).IsRequired();
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.OfficerId).IsRequired();
                entity.Property(e => e.OfficerName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.OfficerRank).HasMaxLength(50);
                entity.Property(e => e.OfficerCode).HasMaxLength(20);
            });

            // تكوين جدول خط السير - مبسط
            modelBuilder.Entity<FieldVisitItinerary>(entity =>
            {
                entity.ToTable("FieldVisitItineraries");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ItineraryText).HasMaxLength(1000);
                entity.HasIndex(e => new { e.FieldVisitId, e.DayNumber }).IsUnique();
            });

            // Configure DriverQuote entity
            modelBuilder.Entity<DriverQuote>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.DriverId).IsRequired();
                entity.Property(e => e.DriverName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.DriverCode).IsRequired().HasMaxLength(20);
                entity.Property(e => e.PhoneNumber).HasMaxLength(20);
                entity.Property(e => e.VehicleType).HasMaxLength(50);
                entity.Property(e => e.VehicleNumber).HasMaxLength(20);
                entity.Property(e => e.QuotedPrice).HasColumnType("decimal(18,2)").IsRequired();
                entity.Property(e => e.QuotedDays).IsRequired();
                entity.Property(e => e.Notes).HasMaxLength(1000);
                entity.Property(e => e.QuoteDate).IsRequired().HasDefaultValueSql("GETDATE()");
                entity.Property(e => e.Status)
                    .HasConversion<int>()
                    .IsRequired()
                    .HasDefaultValue(QuoteStatus.Pending);

                // إضافة خاصية IsSelected للواجهة (لا تحفظ في قاعدة البيانات)
                entity.Ignore(e => e.IsSelected);
                entity.Ignore(e => e.FormattedPrice);
                entity.Ignore(e => e.StatusText);
                entity.Ignore(e => e.StatusColor);

                // Create indexes for performance
                entity.HasIndex(e => e.DriverId).HasDatabaseName("IX_DriverQuote_DriverId");
                entity.HasIndex(e => e.Status).HasDatabaseName("IX_DriverQuote_Status");
                entity.HasIndex(e => e.QuoteDate).HasDatabaseName("IX_DriverQuote_QuoteDate");
            });

            // البيانات الافتراضية يتم إضافتها من خلال InitialDataSeeder
            // SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Create default admin user
            var adminUser = new User
            {
                UserId = 1,
                Username = "admin",
                FullName = "المسئول الرئيسي",
                Email = "<EMAIL>",
                PasswordHash = HashPassword("123456"),
                Role = "Admin",
                IsActive = true,
                CreatedDate = new DateTime(2024, 1, 1),
                CreatedBy = "System"
            };

            modelBuilder.Entity<User>().HasData(adminUser);

            // Create default permissions for admin
            var permissions = new[]
            {
                new UserPermission { PermissionId = 1, UserId = 1, PermissionName = "ViewDashboard", PermissionDescription = "عرض لوحة التحكم", IsGranted = true, CreatedBy = "System" },
                new UserPermission { PermissionId = 2, UserId = 1, PermissionName = "ManageDrivers", PermissionDescription = "إدارة السائقين", IsGranted = true, CreatedBy = "System" },
                new UserPermission { PermissionId = 3, UserId = 1, PermissionName = "ManageUsers", PermissionDescription = "إدارة المستخدمين", IsGranted = true, CreatedBy = "System" },
                new UserPermission { PermissionId = 4, UserId = 1, PermissionName = "ViewReports", PermissionDescription = "عرض التقارير", IsGranted = true, CreatedBy = "System" },
                new UserPermission { PermissionId = 5, UserId = 1, PermissionName = "ManageSettings", PermissionDescription = "إدارة الإعدادات", IsGranted = true, CreatedBy = "System" },
                new UserPermission { PermissionId = 6, UserId = 1, PermissionName = "ManageContracts", PermissionDescription = "إدارة العقود", IsGranted = true, CreatedBy = "System" },
                new UserPermission { PermissionId = 7, UserId = 1, PermissionName = "ManagePricing", PermissionDescription = "إدارة الأسعار", IsGranted = true, CreatedBy = "System" },
                new UserPermission { PermissionId = 8, UserId = 1, PermissionName = "SendMessages", PermissionDescription = "إرسال الرسائل", IsGranted = true, CreatedBy = "System" },
                new UserPermission { PermissionId = 9, UserId = 1, PermissionName = "ViewRoutes", PermissionDescription = "عرض الطرق", IsGranted = true, CreatedBy = "System" },
                new UserPermission { PermissionId = 10, UserId = 1, PermissionName = "ManageDropData", PermissionDescription = "إدارة بيانات النزول", IsGranted = true, CreatedBy = "System" }
            };

            modelBuilder.Entity<UserPermission>().HasData(permissions);

            // Seed Sectors with Excel mapping codes
            var sectors = new[]
            {
                new Sector { Id = 2, Code = "training", Name = "التدريب", Description = "قطاع التدريب " },
                new Sector { Id = 3, Code = "contracts", Name = "التعاقدات", Description = "قطاع التعاقدات " },
                new Sector { Id = 4, Code = "education", Name = "التعليم", Description = "قطاع التعليم " },
                new Sector { Id = 5, Code = "empowerment", Name = "التمكين", Description = "قطاع التمكين " },
                new Sector { Id = 6, Code = "accounts", Name = "الحسابات", Description = "قطاع الحسابات " },
                new Sector { Id = 7, Code = "agriculture", Name = "الزراعة", Description = "قطاع الزراعة" },
                new Sector { Id = 8, Code = "complaints_compliance", Name = "الشكاوى والامتثال", Description = "قطاع الشكاوى والامتثال" },
                new Sector { Id = 9, Code = "health_social_protection", Name = "الصحة والحماية الاجتماعية", Description = "قطاع الصحة والحماية الاجتماعية" },
                new Sector { Id = 10, Code = "roads", Name = "الطرق", Description = "قطاع الطرق والمواصلات" },
                new Sector { Id = 11, Code = "technical", Name = "الفنية", Description = "قطاع الفنية" },
                new Sector { Id = 12, Code = "monitoring_evaluation", Name = "المراقبة والتقييم", Description = "قطاع المراقبة والتقييم" },
                new Sector { Id = 13, Code = "water_environment", Name = "المياه والبيئة", Description = "قطاع المياه والبيئة" },
                new Sector { Id = 14, Code = "cash_for_work", Name = "النقد مقابل العمل", Description = "قطاع النقد مقابل العمل" }
            };

            modelBuilder.Entity<Sector>().HasData(sectors);


            // Configure ProfessionalTemplate entity
            modelBuilder.Entity<ProfessionalTemplate>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Type).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Content).IsRequired();
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Category).HasMaxLength(200);
                entity.Property(e => e.CreatedBy).HasMaxLength(100);
                entity.Property(e => e.ModifiedBy).HasMaxLength(100);
                entity.Property(e => e.Tags).HasMaxLength(1000);

                entity.HasIndex(e => e.Type);
                entity.HasIndex(e => e.Category);
                entity.HasIndex(e => e.IsActive);
            });

            // Configure ContractTemplate entity
            modelBuilder.Entity<ContractTemplate>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ContractIntroduction).HasMaxLength(1000);
                entity.Property(e => e.FirstPartyTemplate).HasMaxLength(1000);
                entity.Property(e => e.SecondPartyTemplate).HasMaxLength(1000);
                entity.Property(e => e.VehicleSpecsTemplate).HasMaxLength(1000);
                entity.Property(e => e.PurposeTemplate).HasMaxLength(1000);
                entity.Property(e => e.DurationTemplate).HasMaxLength(1000);
                entity.Property(e => e.PriceTemplate).HasMaxLength(1000);
                entity.Property(e => e.OwnershipTemplate).HasMaxLength(1000);
                entity.Property(e => e.ObligationsTemplate).HasMaxLength(2000);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETDATE()");
                entity.Property(e => e.LastModified).HasDefaultValueSql("GETDATE()");
            });

            // Configure MessageDocumentation entity
            modelBuilder.Entity<MessageDocumentation>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.VisitNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.ReportNumber).HasMaxLength(50);
                entity.Property(e => e.VisitConductor1).HasMaxLength(100);
                entity.Property(e => e.VisitConductor2).HasMaxLength(100);
                entity.Property(e => e.VisitConductor3).HasMaxLength(100);
                entity.Property(e => e.FirstOfficer).HasMaxLength(100);
                entity.Property(e => e.SecondOfficer).HasMaxLength(100);
                entity.Property(e => e.ThirdOfficer).HasMaxLength(100);
                entity.Property(e => e.Notes).HasMaxLength(2000);
                entity.Property(e => e.Status).HasMaxLength(50).HasDefaultValue("مسودة");
                entity.Property(e => e.ImagesFolderPath).HasMaxLength(500);
                entity.Property(e => e.ImagePath1).HasMaxLength(500);
                entity.Property(e => e.ImagePath2).HasMaxLength(500);
                entity.Property(e => e.ImagePath3).HasMaxLength(500);
                entity.Property(e => e.ImagePath4).HasMaxLength(500);
                entity.Property(e => e.ImagePath5).HasMaxLength(500);
                entity.Property(e => e.ImagePath6).HasMaxLength(500);
                entity.Property(e => e.ImagesCount).HasDefaultValue(0);
                entity.Property(e => e.DocumentationDate).IsRequired().HasDefaultValueSql("GETDATE()");
                entity.Property(e => e.CreatedDate).IsRequired().HasDefaultValueSql("GETDATE()");
                entity.Property(e => e.LastModified).IsRequired().HasDefaultValueSql("GETDATE()");

                entity.HasIndex(e => e.VisitNumber);
                entity.HasIndex(e => e.ReportNumber);
                entity.HasIndex(e => e.DocumentationDate);
            });

            // Configure MessageAttachment entity
            modelBuilder.Entity<MessageAttachment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.MessageDocumentationId).IsRequired();
                entity.Property(e => e.FileName).IsRequired().HasMaxLength(255);
                entity.Property(e => e.FilePath).IsRequired().HasMaxLength(500);
                entity.Property(e => e.FileType).HasMaxLength(50);
                entity.Property(e => e.FileSize).IsRequired();
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.AddedDate).IsRequired().HasDefaultValueSql("GETDATE()");

                // Configure relationship
                entity.HasOne(e => e.MessageDocumentation)
                      .WithMany(d => d.Attachments)
                      .HasForeignKey(e => e.MessageDocumentationId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.MessageDocumentationId);
                entity.HasIndex(e => e.FileType);
            });

            // Seed Professional Templates
            var professionalTemplates = new[]
            {
                // User Templates
                new ProfessionalTemplate
                {
                    Id = 1,
                    Name = "نموذج مستخدم إداري",
                    Type = "UserTemplate",
                    Content = "نموذج احترافي لإنشاء مستخدم إداري مع صلاحيات كاملة",
                    Description = "نموذج مخصص للمستخدمين الإداريين",
                    Category = "إداري",
                    IsDefault = true,
                    CreatedBy = "System"
                },
                new ProfessionalTemplate
                {
                    Id = 2,
                    Name = "نموذج مستخدم عادي",
                    Type = "UserTemplate",
                    Content = "نموذج احترافي لإنشاء مستخدم عادي مع صلاحيات محدودة",
                    Description = "نموذج مخصص للمستخدمين العاديين",
                    Category = "عام",
                    IsDefault = true,
                    CreatedBy = "System"
                },

                // Message Templates
                new ProfessionalTemplate
                {
                    Id = 3,
                    Name = "قالب رسالة الزيارة الميدانية",
                    Type = "MessageTemplate",
                    Content = "الأخ/{DriverName} المحترم،\nيرجى تقديم عرض سعركم خلال 24 ساعة وذلك للسفر لمدة ({DaysCount} يوم) مع الأخـ:ت: {Visitors}\nفي المناطق التالية:\n{Itinerary}\n\n📅 تاريخ النزول: {StartDate}...وشكراً",
                    Description = "قالب لرسائل الزيارات الميدانية",
                    Category = "زيارات ميدانية",
                    IsDefault = true,
                    CreatedBy = "System"
                },
                new ProfessionalTemplate
                {
                    Id = 4,
                    Name = "قالب رسالة مختصر",
                    Type = "MessageTemplate",
                    Content = "الأخ/{DriverName} المحترم،\nمطلوب عرض سعر للسفر ({DaysCount} يوم) مع {Visitors}\nالتاريخ: {StartDate} - {EndDate}\nوشكراً",
                    Description = "قالب مختصر للرسائل السريعة",
                    Category = "عام",
                    IsDefault = true,
                    CreatedBy = "System"
                },

                // Report Templates
                new ProfessionalTemplate
                {
                    Id = 5,
                    Name = "قالب تقرير الزيارة الميدانية",
                    Type = "ReportTemplate",
                    Content = "تقرير احترافي شامل للزيارة الميدانية مع جميع التفاصيل والبيانات",
                    Description = "قالب تقرير شامل للزيارات الميدانية",
                    Category = "تقارير",
                    IsDefault = true,
                    CreatedBy = "System"
                },
                new ProfessionalTemplate
                {
                    Id = 6,
                    Name = "قالب عقد السائق",
                    Type = "ReportTemplate",
                    Content = "قالب لعقد السائق مع جميع الشروط والأحكام",
                    Description = "قالب عقد احترافي للسائقين",
                    Category = "عقود",
                    IsDefault = true,
                    CreatedBy = "System"
                }
            };

            modelBuilder.Entity<ProfessionalTemplate>().HasData(professionalTemplates);
        }

        private string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                await Database.CanConnectAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task InitializeDatabaseAsync()
        {
            try
            {
                // التحقق من وجود قاعدة البيانات أولاً
                var dbExists = await Data.DatabaseConfig.DatabaseExists();
                if (!dbExists)
                {
                    // إنشاء قاعدة البيانات باستخدام النظام الموحد
                    var created = await Data.DatabaseConfig.CreateDatabaseIfNotExists();
                    if (!created)
                    {
                        throw new Exception("فشل في إنشاء قاعدة البيانات");
                    }
                }

                // التأكد من إنشاء الجداول فقط (بدون إنشاء قاعدة البيانات)
                await Database.EnsureCreatedAsync();

                // تطبيق Migrations المطلوبة
                await ApplyMigrationsAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تهيئة قاعدة البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق Migrations المطلوبة
        /// </summary>
        private async Task ApplyMigrationsAsync()
        {
            try
            {
                // تطبيق migration لإضافة عمود نص الرسالة للسائق الفائز
                await Migrations.AddWinnerDriverMessageColumn.ApplyMigrationAsync(this);

                // تطبيق migration لإضافة عمود خط السير النصي
                await Migrations.AddItineraryTextColumn.ApplyMigrationAsync(this);

                // تطبيق migration لإضافة حقول الموافقة ووقت الإرسال
                await Migrations.AddApprovalAndSubmissionFields.ApplyAsync(this);

                // تطبيق migration لإضافة جداول توثيق الرسائل
                await Migrations.AddMessageDocumentationTables.ApplyMigrationAsync(this);

                System.Diagnostics.Debug.WriteLine("✅ تم تطبيق جميع Migrations بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تطبيق Migrations: {ex.Message}");
                // لا نرمي الخطأ هنا لتجنب توقف التطبيق
            }
        }
    }
}
