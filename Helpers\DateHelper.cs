using System;
using System.Globalization;

namespace DriverManagementSystem.Helpers
{
    /// <summary>
    /// مساعد التواريخ - دوال مساعدة للتعامل مع التواريخ
    /// </summary>
    public static class DateHelper
    {
        /// <summary>
        /// الحصول على اسم اليوم بالعربي من التاريخ
        /// </summary>
        /// <param name="date">التاريخ</param>
        /// <returns>اسم اليوم بالعربي</returns>
        public static string GetArabicDayName(DateTime date)
        {
            try
            {
                var dayOfWeek = date.DayOfWeek;
                
                return dayOfWeek switch
                {
                    DayOfWeek.Sunday => "الأحد",
                    DayOfWeek.Monday => "الاثنين",
                    DayOfWeek.Tuesday => "الثلاثاء",
                    DayOfWeek.Wednesday => "الأربعاء",
                    DayOfWeek.Thursday => "الخميس",
                    DayOfWeek.Friday => "الجمعة",
                    DayOfWeek.Saturday => "السبت",
                    _ => "غير محدد"
                };
            }
            catch
            {
                return "غير محدد";
            }
        }

        /// <summary>
        /// الحصول على اسم اليوم بالعربي من نص التاريخ
        /// </summary>
        /// <param name="dateString">التاريخ كنص (dd/MM/yyyy)</param>
        /// <returns>اسم اليوم بالعربي</returns>
        public static string GetArabicDayName(string dateString)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(dateString))
                    return "غير محدد";

                // محاولة تحويل النص إلى تاريخ
                if (DateTime.TryParseExact(dateString, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
                {
                    return GetArabicDayName(date);
                }
                
                // محاولة أخرى بصيغ مختلفة
                if (DateTime.TryParse(dateString, out date))
                {
                    return GetArabicDayName(date);
                }

                return "غير محدد";
            }
            catch
            {
                return "غير محدد";
            }
        }

        /// <summary>
        /// تكوين نص "أنه في يوم" مع اسم اليوم والتاريخ
        /// </summary>
        /// <param name="date">التاريخ</param>
        /// <returns>النص الكامل مثل "أنه في يوم الأحد الموافق 15/12/2024"</returns>
        public static string GetFullDateText(DateTime date)
        {
            try
            {
                var dayName = GetArabicDayName(date);
                var dateText = date.ToString("dd/MM/yyyy");
                
                return $"أنه في يوم {dayName} الموافق {dateText}";
            }
            catch
            {
                return $"أنه في يوم غير محدد الموافق {DateTime.Now:dd/MM/yyyy}";
            }
        }

        /// <summary>
        /// تكوين نص "أنه في يوم" من نص التاريخ
        /// </summary>
        /// <param name="dateString">التاريخ كنص</param>
        /// <returns>النص الكامل</returns>
        public static string GetFullDateText(string dateString)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(dateString))
                    return GetFullDateText(DateTime.Now);

                if (DateTime.TryParseExact(dateString, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
                {
                    return GetFullDateText(date);
                }
                
                if (DateTime.TryParse(dateString, out date))
                {
                    return GetFullDateText(date);
                }

                // إذا فشل التحويل، استخدم التاريخ الحالي
                return GetFullDateText(DateTime.Now);
            }
            catch
            {
                return GetFullDateText(DateTime.Now);
            }
        }

        /// <summary>
        /// الحصول على اسم الشهر بالعربي
        /// </summary>
        /// <param name="month">رقم الشهر (1-12)</param>
        /// <returns>اسم الشهر بالعربي</returns>
        public static string GetArabicMonthName(int month)
        {
            return month switch
            {
                1 => "يناير",
                2 => "فبراير", 
                3 => "مارس",
                4 => "أبريل",
                5 => "مايو",
                6 => "يونيو",
                7 => "يوليو",
                8 => "أغسطس",
                9 => "سبتمبر",
                10 => "أكتوبر",
                11 => "نوفمبر",
                12 => "ديسمبر",
                _ => "غير محدد"
            };
        }

        /// <summary>
        /// تحويل التاريخ إلى صيغة عربية كاملة
        /// </summary>
        /// <param name="date">التاريخ</param>
        /// <returns>التاريخ بالعربي مثل "الأحد 15 ديسمبر 2024"</returns>
        public static string GetFullArabicDate(DateTime date)
        {
            try
            {
                var dayName = GetArabicDayName(date);
                var monthName = GetArabicMonthName(date.Month);
                
                return $"{dayName} {date.Day} {monthName} {date.Year}";
            }
            catch
            {
                return "تاريخ غير صحيح";
            }
        }

        /// <summary>
        /// تحويل نص التاريخ إلى صيغة عربية كاملة
        /// </summary>
        /// <param name="dateString">التاريخ كنص</param>
        /// <returns>التاريخ بالعربي</returns>
        public static string GetFullArabicDate(string dateString)
        {
            try
            {
                if (DateTime.TryParseExact(dateString, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime date))
                {
                    return GetFullArabicDate(date);
                }
                
                if (DateTime.TryParse(dateString, out date))
                {
                    return GetFullArabicDate(date);
                }

                return "تاريخ غير صحيح";
            }
            catch
            {
                return "تاريخ غير صحيح";
            }
        }
    }
}
