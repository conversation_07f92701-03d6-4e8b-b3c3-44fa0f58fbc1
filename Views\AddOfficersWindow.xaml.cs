using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة إضافة الضباط الجدد
    /// </summary>
    public partial class AddOfficersWindow : Window
    {
        private readonly IDataService _dataService;
        private bool _isProcessing = false;

        public AddOfficersWindow()
        {
            InitializeComponent();
            _dataService = new DatabaseService();
            
            // تحديث العنوان
            Title = "إضافة الضباط الجدد - نظام إدارة السائقين";
        }

        /// <summary>
        /// بدء عملية إضافة الضباط
        /// </summary>
        private async void StartButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isProcessing) return;

            try
            {
                _isProcessing = true;
                StartButton.IsEnabled = false;
                ViewOfficersButton.IsEnabled = false;

                // إعداد واجهة المستخدم
                ShowStatus("🚀 بدء عملية إضافة الضباط...", "#007ACC");
                UpdateLog("🚀 بدء عملية إضافة الضباط الجدد...\n");
                
                ProgressBar.Value = 0;
                ProgressText.Text = "جاري التحضير...";

                // إنشاء خدمة إضافة الضباط
                var seeder = new NewOfficersSeeder(_dataService);

                // تحديث التقدم
                ProgressBar.Value = 10;
                ProgressText.Text = "جاري فحص القطاعات...";
                UpdateLog("🔍 فحص القطاعات الموجودة...\n");

                await Task.Delay(500); // محاكاة وقت المعالجة

                // تحديث التقدم
                ProgressBar.Value = 30;
                ProgressText.Text = "جاري إضافة الضباط...";
                UpdateLog("👥 بدء إضافة الضباط...\n");

                // تنفيذ عملية الإضافة
                var addedCount = await seeder.SeedNewOfficersAsync();

                // تحديث التقدم
                ProgressBar.Value = 90;
                ProgressText.Text = "جاري إنهاء العملية...";

                await Task.Delay(500);

                // إنهاء العملية
                ProgressBar.Value = 100;
                ProgressText.Text = "تم الانتهاء بنجاح!";

                // تحديث الإحصائيات
                AddedOfficersText.Text = addedCount.ToString();
                var skippedCount = 37 - addedCount; // إجمالي الضباط - المضافين = المتخطين
                SkippedOfficersText.Text = skippedCount.ToString();

                // عرض النتيجة النهائية
                if (addedCount > 0)
                {
                    ShowStatus($"✅ تم إضافة {addedCount} ضابط بنجاح!", "#28A745");
                    UpdateLog($"✅ تم الانتهاء بنجاح! تم إضافة {addedCount} ضابط جديد.\n");
                    
                    if (skippedCount > 0)
                    {
                        UpdateLog($"⚠️ تم تخطي {skippedCount} ضابط (موجود مسبقاً).\n");
                    }
                }
                else
                {
                    ShowStatus("ℹ️ جميع الضباط موجودون مسبقاً", "#FFC107");
                    UpdateLog("ℹ️ جميع الضباط موجودون مسبقاً في قاعدة البيانات.\n");
                }

                ViewOfficersButton.IsEnabled = true;

                // عرض رسالة نجاح
                MessageBox.Show(
                    $"تم الانتهاء من عملية إضافة الضباط!\n\n" +
                    $"✅ تم إضافة: {addedCount} ضابط\n" +
                    $"⚠️ تم تخطي: {skippedCount} ضابط (موجود مسبقاً)",
                    "نجحت العملية",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
            }
            catch (Exception ex)
            {
                // معالجة الأخطاء
                ProgressBar.Value = 0;
                ProgressText.Text = "حدث خطأ!";
                
                ShowStatus("❌ حدث خطأ أثناء العملية", "#DC3545");
                UpdateLog($"❌ خطأ: {ex.Message}\n");

                MessageBox.Show(
                    $"حدث خطأ أثناء إضافة الضباط:\n\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
            finally
            {
                _isProcessing = false;
                StartButton.IsEnabled = true;
            }
        }

        /// <summary>
        /// عرض نافذة إدارة الضباط
        /// </summary>
        private void ViewOfficersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var sectorWindow = new SectorManagementWindow();
                sectorWindow.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في فتح نافذة إدارة الضباط:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isProcessing)
            {
                var result = MessageBox.Show(
                    "العملية قيد التنفيذ. هل تريد إغلاق النافذة؟",
                    "تأكيد الإغلاق",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );

                if (result != MessageBoxResult.Yes)
                    return;
            }

            Close();
        }

        /// <summary>
        /// عرض حالة العملية
        /// </summary>
        private void ShowStatus(string message, string color)
        {
            StatusBorder.Visibility = Visibility.Visible;
            StatusText.Text = message;
            StatusText.Foreground = (SolidColorBrush)new BrushConverter().ConvertFrom(color);
            
            // تحديد الأيقونة حسب نوع الرسالة
            if (message.Contains("✅"))
                StatusIcon.Text = "✅";
            else if (message.Contains("❌"))
                StatusIcon.Text = "❌";
            else if (message.Contains("⚠️"))
                StatusIcon.Text = "⚠️";
            else if (message.Contains("🚀"))
                StatusIcon.Text = "🚀";
            else
                StatusIcon.Text = "ℹ️";
        }

        /// <summary>
        /// تحديث سجل العمليات
        /// </summary>
        private void UpdateLog(string message)
        {
            Dispatcher.Invoke(() =>
            {
                if (LogTextBlock.Text == "انتظار بدء العملية...")
                    LogTextBlock.Text = "";
                
                LogTextBlock.Text += $"[{DateTime.Now:HH:mm:ss}] {message}";
                
                // التمرير إلى الأسفل
                if (LogTextBlock.Parent is ScrollViewer scrollViewer)
                {
                    scrollViewer.ScrollToEnd();
                }
            });
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            _dataService?.Dispose();
            base.OnClosed(e);
        }
    }
}
