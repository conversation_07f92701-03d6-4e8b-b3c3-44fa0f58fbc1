using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DriverManagementSystem.Models
{
    [Table("Users")]
    public class User : INotifyPropertyChanged
    {
        private string _fullName = string.Empty;
        private string _username = string.Empty;
        private string _role = "User";
        private bool _isActive = true;

        [Key]
        public int UserId { get; set; }

        [Required]
        [StringLength(50)]
        public string Username
        {
            get => _username;
            set
            {
                _username = value;
                OnPropertyChanged();
            }
        }

        [Required]
        [StringLength(255)]
        public string PasswordHash { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string FullName
        {
            get => _fullName;
            set
            {
                _fullName = value;
                OnPropertyChanged();
            }
        }

        [Required]
        [StringLength(100)]
        public string Email { get; set; } = string.Empty;

        [StringLength(500)]
        public string ProfileImagePath { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string Role
        {
            get => _role;
            set
            {
                _role = value;
                OnPropertyChanged();
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                _isActive = value;
                OnPropertyChanged();
            }
        }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? LastLoginDate { get; set; }

        [StringLength(50)]
        public string? CreatedBy { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        // Navigation properties
        public virtual ICollection<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    [Table("UserPermissions")]
    public class UserPermission
    {
        [Key]
        public int PermissionId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        [StringLength(50)]
        public string PermissionName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string PermissionDescription { get; set; } = string.Empty;

        public bool IsGranted { get; set; } = false;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [StringLength(50)]
        public string? CreatedBy { get; set; }

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;
    }

    public enum UserRole
    {
        Admin,
        Manager,
        User,
        Viewer
    }

    public enum Permission
    {
        ViewDashboard,
        ManageDrivers,
        ManageUsers,
        ViewReports,
        ManageSettings,
        ManageContracts,
        ManagePricing,
        SendMessages,
        ViewRoutes,
        ManageDropData
    }
}
