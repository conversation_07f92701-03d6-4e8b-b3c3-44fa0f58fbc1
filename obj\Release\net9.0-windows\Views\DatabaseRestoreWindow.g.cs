﻿#pragma checksum "..\..\..\..\Views\DatabaseRestoreWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "8E116D6A79BA1FA4C8857BCE8533993CDDBAF5CA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DriverManagementSystem.Views {
    
    
    /// <summary>
    /// DatabaseRestoreWindow
    /// </summary>
    public partial class DatabaseRestoreWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 47 "..\..\..\..\Views\DatabaseRestoreWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock VisitsCountText;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\Views\DatabaseRestoreWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DriversCountText;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\Views\DatabaseRestoreWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectsCountText;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\Views\DatabaseRestoreWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SectorsCountText;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\Views\DatabaseRestoreWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OfficersCountText;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\Views\DatabaseRestoreWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UsersCountText;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\Views\DatabaseRestoreWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\Views\DatabaseRestoreWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RestoreButton;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\Views\DatabaseRestoreWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetButton;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\Views\DatabaseRestoreWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LogTextBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SFDSystem;V2.0.0.0;component/views/databaserestorewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\DatabaseRestoreWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.VisitsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.DriversCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.ProjectsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.SectorsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.OfficersCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.UsersCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 82 "..\..\..\..\Views\DatabaseRestoreWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.RestoreButton = ((System.Windows.Controls.Button)(target));
            
            #line 99 "..\..\..\..\Views\DatabaseRestoreWindow.xaml"
            this.RestoreButton.Click += new System.Windows.RoutedEventHandler(this.RestoreButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.ResetButton = ((System.Windows.Controls.Button)(target));
            
            #line 116 "..\..\..\..\Views\DatabaseRestoreWindow.xaml"
            this.ResetButton.Click += new System.Windows.RoutedEventHandler(this.ResetButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.LogTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            
            #line 160 "..\..\..\..\Views\DatabaseRestoreWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

