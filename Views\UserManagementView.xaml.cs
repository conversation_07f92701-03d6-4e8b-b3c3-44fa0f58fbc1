using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    public partial class UserManagementView : UserControl
    {
        private IUserService? _userService;
        private List<User> _allUsers = new List<User>();

        public UserManagementView()
        {
            InitializeComponent();
            InitializeService();
        }

        private void InitializeService()
        {
            try
            {
                var context = new ApplicationDbContext();
                _userService = new UserService(context);

                // Load users in background
                Dispatcher.BeginInvoke(new Action(async () =>
                {
                    try
                    {
                        await context.InitializeDatabaseAsync();
                        await LoadUsersAsync();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة الخدمة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadUsersAsync()
        {
            try
            {
                if (_userService != null)
                {
                    _allUsers = await _userService.GetAllUsersAsync();
                    DisplayUsers(_allUsers);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المستخدمين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadUsers()
        {
            _ = LoadUsersAsync();
        }

        private void DisplayUsers(List<User> users)
        {
            UsersPanel.Children.Clear();

            // Hide loading message
            LoadingMessage.Visibility = Visibility.Collapsed;

            if (users == null || users.Count == 0)
            {
                NoUsersMessage.Visibility = Visibility.Visible;
                return;
            }

            NoUsersMessage.Visibility = Visibility.Collapsed;

            foreach (var user in users)
            {
                var userCard = CreateUserCard(user);
                UsersPanel.Children.Add(userCard);
            }
        }

        private Border CreateUserCard(User user)
        {
            var card = new Border
            {
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(224, 224, 224)),
                BorderThickness = new Thickness(1, 1, 1, 1),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(20, 20, 20, 20),
                Margin = new Thickness(0, 0, 0, 15),
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = Colors.Gray,
                    Direction = 270,
                    ShadowDepth = 2,
                    Opacity = 0.1,
                    BlurRadius = 8
                }
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            // User Avatar
            var avatar = new Border
            {
                Width = 50,
                Height = 50,
                CornerRadius = new CornerRadius(25),
                Background = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                Margin = new Thickness(0, 0, 15, 0)
            };
            Grid.SetColumn(avatar, 0);

            var avatarIcon = new TextBlock
            {
                Text = GetUserIcon(user.Role),
                FontSize = 20,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            avatar.Child = avatarIcon;

            // User Info
            var infoPanel = new StackPanel { VerticalAlignment = VerticalAlignment.Center };
            Grid.SetColumn(infoPanel, 1);

            infoPanel.Children.Add(new TextBlock
            {
                Text = user.FullName,
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(51, 51, 51))
            });

            infoPanel.Children.Add(new TextBlock
            {
                Text = $"@{user.Username} - {GetRoleDisplayName(user.Role)}",
                FontSize = 12,
                Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)),
                Margin = new Thickness(0, 2, 0, 0)
            });

            infoPanel.Children.Add(new TextBlock
            {
                Text = user.Email,
                FontSize = 11,
                Foreground = new SolidColorBrush(Color.FromRgb(153, 153, 153)),
                Margin = new Thickness(0, 2, 0, 0)
            });

            // Add last login info
            var lastLoginText = user.LastLoginDate.HasValue
                ? $"آخر دخول: {user.LastLoginDate.Value:yyyy/MM/dd HH:mm}"
                : "لم يسجل دخول من قبل";

            infoPanel.Children.Add(new TextBlock
            {
                Text = lastLoginText,
                FontSize = 10,
                Foreground = new SolidColorBrush(Color.FromRgb(128, 128, 128)),
                Margin = new Thickness(0, 2, 0, 0),
                FontStyle = FontStyles.Italic
            });

            // Status
            var statusBorder = new Border
            {
                Background = new SolidColorBrush(user.IsActive ? Color.FromRgb(76, 175, 80) : Color.FromRgb(158, 158, 158)),
                CornerRadius = new CornerRadius(12),
                Padding = new Thickness(8, 4, 8, 4),
                Margin = new Thickness(10, 0, 10, 0)
            };
            Grid.SetColumn(statusBorder, 2);

            statusBorder.Child = new TextBlock
            {
                Text = user.IsActive ? "نشط" : "غير نشط",
                FontSize = 11,
                Foreground = Brushes.White,
                FontWeight = FontWeights.Bold
            };

            // Actions
            var actionsPanel = new StackPanel { Orientation = Orientation.Horizontal };
            Grid.SetColumn(actionsPanel, 3);

            var editButton = new Button
            {
                Content = "✏️ تعديل",
                Background = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                Foreground = Brushes.White,
                Tag = user
            };
            editButton.Click += EditUser_Click;
            editButton.Style = (Style)FindResource("ActionButtonStyle");

            var permissionsButton = new Button
            {
                Content = "🔑 الصلاحيات",
                Background = new SolidColorBrush(Color.FromRgb(255, 152, 0)),
                Foreground = Brushes.White,
                Tag = user
            };
            permissionsButton.Click += ManagePermissions_Click;
            permissionsButton.Style = (Style)FindResource("ActionButtonStyle");

            var deleteButton = new Button
            {
                Content = "🗑️ حذف",
                Background = new SolidColorBrush(Color.FromRgb(244, 67, 54)),
                Foreground = Brushes.White,
                Tag = user
            };
            deleteButton.Click += DeleteUser_Click;
            deleteButton.Style = (Style)FindResource("ActionButtonStyle");

            actionsPanel.Children.Add(editButton);
            actionsPanel.Children.Add(permissionsButton);
            if (user.Username != "admin") // Don't allow deleting admin
                actionsPanel.Children.Add(deleteButton);

            grid.Children.Add(avatar);
            grid.Children.Add(infoPanel);
            grid.Children.Add(statusBorder);
            grid.Children.Add(actionsPanel);

            card.Child = grid;
            return card;
        }

        private string GetUserIcon(string role)
        {
            return role.ToLower() switch
            {
                "admin" => "👨‍💼",
                "manager" => "👨‍💻",
                "user" => "👤",
                "viewer" => "👁️",
                _ => "👤"
            };
        }

        private string GetRoleDisplayName(string role)
        {
            return role.ToLower() switch
            {
                "admin" => "مسئول النظام",
                "manager" => "مشرف",
                "user" => "مستخدم",
                "viewer" => "مشاهد",
                _ => "مستخدم"
            };
        }

        private void AddUserButton_Click(object sender, RoutedEventArgs e)
        {
            if (_userService != null)
            {
                var addUserWindow = new AddUserWindow(_userService);
                addUserWindow.UserAdded += (s, args) => LoadUsers();
                addUserWindow.ShowDialog();
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadUsers();
        }

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SearchTextBox.Text == "🔍 البحث في المستخدمين...")
            {
                SearchTextBox.Text = "";
                SearchTextBox.Foreground = Brushes.Black;
            }
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                SearchTextBox.Text = "🔍 البحث في المستخدمين...";
                SearchTextBox.Foreground = new SolidColorBrush(Color.FromRgb(153, 153, 153));
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                if (SearchTextBox.Text == "🔍 البحث في المستخدمين..." || string.IsNullOrWhiteSpace(SearchTextBox.Text))
                {
                    DisplayUsers(_allUsers);
                    return;
                }

                if (_allUsers == null || _allUsers.Count == 0)
                {
                    return;
                }

                var searchText = SearchTextBox.Text.ToLower();
                var filteredUsers = _allUsers.Where(u =>
                    (u.FullName?.ToLower().Contains(searchText) ?? false) ||
                    (u.Username?.ToLower().Contains(searchText) ?? false) ||
                    (u.Email?.ToLower().Contains(searchText) ?? false) ||
                    GetRoleDisplayName(u.Role ?? "").ToLower().Contains(searchText)
                ).ToList();

                DisplayUsers(filteredUsers);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void EditUser_Click(object sender, RoutedEventArgs e)
        {
            if (_userService != null)
            {
                var user = (User)((Button)sender).Tag;
                var editUserWindow = new EditUserWindow(_userService, user);
                editUserWindow.UserUpdated += (s, args) => LoadUsers();
                editUserWindow.ShowDialog();
            }
        }

        private void ManagePermissions_Click(object sender, RoutedEventArgs e)
        {
            if (_userService != null)
            {
                var user = (User)((Button)sender).Tag;
                var permissionsWindow = new UserPermissionsWindow(_userService, user);
                permissionsWindow.ShowDialog();
            }
        }

        private async void DeleteUser_Click(object sender, RoutedEventArgs e)
        {
            if (_userService != null)
            {
                var user = (User)((Button)sender).Tag;

                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المستخدم '{user.FullName}'؟\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var success = await _userService.DeleteUserAsync(user.UserId);
                        if (success)
                        {
                            MessageBox.Show("تم حذف المستخدم بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                            LoadUsers();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف المستخدم!", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف المستخدم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
    }
}
