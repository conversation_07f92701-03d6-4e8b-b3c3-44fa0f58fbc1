﻿#pragma checksum "..\..\..\..\Views\EnhancedEditUserWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3CEDCCE513A5FE3F209C47F9CA3486C4B216016A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DriverManagementSystem.Views {
    
    
    /// <summary>
    /// EnhancedEditUserWindow
    /// </summary>
    public partial class EnhancedEditUserWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 123 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderTitle;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderSubtitle;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border UserAvatarHeader;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserIconHeader;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserNameHeader;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserDetailsHeader;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FullNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UsernameTextBox;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UsernameNote;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox PasswordBox;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox ConfirmPasswordBox;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PasswordError;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox RoleComboBox;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RoleWarning;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsActiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveStatusDescription;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 331 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CreatedDateText;
        
        #line default
        #line hidden
        
        
        #line 336 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastLoginText;
        
        #line default
        #line hidden
        
        
        #line 347 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 354 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetPasswordButton;
        
        #line default
        #line hidden
        
        
        #line 361 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SFDSystem;V2.0.0.0;component/views/enhancededituserwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HeaderTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.HeaderSubtitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.UserAvatarHeader = ((System.Windows.Controls.Border)(target));
            return;
            case 4:
            this.UserIconHeader = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.UserNameHeader = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.UserDetailsHeader = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.FullNameTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 169 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
            this.FullNameTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ValidateForm);
            
            #line default
            #line hidden
            return;
            case 8:
            this.UsernameTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 177 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
            this.UsernameTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ValidateForm);
            
            #line default
            #line hidden
            return;
            case 9:
            this.UsernameNote = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.EmailTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 187 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
            this.EmailTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ValidateForm);
            
            #line default
            #line hidden
            return;
            case 11:
            this.PasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            
            #line 216 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
            this.PasswordBox.PasswordChanged += new System.Windows.RoutedEventHandler(this.ValidateForm);
            
            #line default
            #line hidden
            return;
            case 12:
            this.ConfirmPasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            
            #line 224 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
            this.ConfirmPasswordBox.PasswordChanged += new System.Windows.RoutedEventHandler(this.ValidateForm);
            
            #line default
            #line hidden
            return;
            case 13:
            this.PasswordError = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.RoleComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 247 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
            this.RoleComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.RoleComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 15:
            this.RoleWarning = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.IsActiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 263 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
            this.IsActiveCheckBox.Checked += new System.Windows.RoutedEventHandler(this.ActiveStatus_Changed);
            
            #line default
            #line hidden
            
            #line 264 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
            this.IsActiveCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.ActiveStatus_Changed);
            
            #line default
            #line hidden
            return;
            case 17:
            this.ActiveStatusDescription = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.CreatedDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.LastLoginText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 353 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.ResetPasswordButton = ((System.Windows.Controls.Button)(target));
            
            #line 360 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
            this.ResetPasswordButton.Click += new System.Windows.RoutedEventHandler(this.ResetPasswordButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 366 "..\..\..\..\Views\EnhancedEditUserWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

