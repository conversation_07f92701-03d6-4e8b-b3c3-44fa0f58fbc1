﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SFDSystem.Migrations
{
    /// <inheritdoc />
    public partial class AddProjectDaysToFieldVisitProject : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // إضافة عمود ProjectDays فقط إذا لم يكن موجوداً
            migrationBuilder.Sql(@"
                PRAGMA table_info(FieldVisitProjects);
            ");

            migrationBuilder.AddColumn<int>(
                name: "ProjectDays",
                table: "FieldVisitProjects",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            // لا نحتاج إنشاء جداول جديدة في هذا Migration
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ProjectDays",
                table: "FieldVisitProjects");
        }
    }
}
