using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة إضافة الضباط الجدد إلى قاعدة البيانات
    /// </summary>
    public class NewOfficersSeeder
    {
        private readonly IDataService _dataService;

        public NewOfficersSeeder(IDataService dataService)
        {
            _dataService = dataService;
        }

        /// <summary>
        /// إضافة جميع الضباط الجدد
        /// </summary>
        public async Task<int> SeedNewOfficersAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🚀 بدء إضافة الضباط الجدد...");

                // الحصول على القطاعات من قاعدة البيانات
                var sectors = await _dataService.GetSectorsAsync();
                var sectorMap = sectors.ToDictionary(s => s.Name, s => s.Id);

                // إضافة القطاعات المفقودة إذا لم تكن موجودة
                await EnsureSectorsExist(sectorMap);

                // البيانات الجديدة
                var newOfficers = GetNewOfficersData(sectorMap);

                int addedCount = 0;
                int skippedCount = 0;

                foreach (var officer in newOfficers)
                {
                    try
                    {
                        // التحقق من عدم وجود الضابط مسبقاً (بناءً على الكود)
                        var existingOfficer = await _dataService.GetOfficerByCodeAsync(officer.Code);
                        if (existingOfficer != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ الضابط موجود مسبقاً: {officer.Name} - الكود: {officer.Code}");
                            skippedCount++;
                            continue;
                        }

                        // إضافة الضابط الجديد
                        var success = await _dataService.AddOfficerAsync(officer);
                        if (success)
                        {
                            addedCount++;
                            System.Diagnostics.Debug.WriteLine($"✅ تم إضافة الضابط: {officer.Name} - الكود: {officer.Code}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ فشل في إضافة الضابط: {officer.Name}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة الضابط {officer.Name}: {ex.Message}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"🎉 تم الانتهاء من إضافة الضباط:");
                System.Diagnostics.Debug.WriteLine($"   - تم إضافة: {addedCount} ضابط");
                System.Diagnostics.Debug.WriteLine($"   - تم تخطي: {skippedCount} ضابط (موجود مسبقاً)");

                return addedCount;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ عام في إضافة الضباط: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// التأكد من وجود جميع القطاعات المطلوبة
        /// </summary>
        private async Task EnsureSectorsExist(Dictionary<string, int> sectorMap)
        {
            var requiredSectors = new[]
            {
                "التعليم",
                "الصحة والحماية الاجتماعية", 
                "الطرق",
                "النقد مقابل العمل",
                "المياه والبيئة",
                "المراقبة والتقييم",
                "الزراعة",
                "التدريب",
                "الشكاوى والامتثال"
            };

            foreach (var sectorName in requiredSectors)
            {
                if (!sectorMap.ContainsKey(sectorName))
                {
                    var newSector = new Sector
                    {
                        Name = sectorName,
                        Code = GenerateSectorCode(sectorName),
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    };

                    var success = await _dataService.AddSectorAsync(newSector);
                    if (success)
                    {
                        // إعادة تحميل القطاعات
                        var sectors = await _dataService.GetSectorsAsync();
                        var newSectorId = sectors.FirstOrDefault(s => s.Name == sectorName)?.Id ?? 1;
                        sectorMap[sectorName] = newSectorId;
                        System.Diagnostics.Debug.WriteLine($"✅ تم إضافة القطاع الجديد: {sectorName}");
                    }
                }
            }
        }

        /// <summary>
        /// توليد كود القطاع
        /// </summary>
        private string GenerateSectorCode(string sectorName)
        {
            var codeMap = new Dictionary<string, string>
            {
                { "التعليم", "EDU" },
                { "الصحة والحماية الاجتماعية", "HLT" },
                { "الطرق", "RDS" },
                { "النقد مقابل العمل", "CFW" },
                { "المياه والبيئة", "WTR" },
                { "المراقبة والتقييم", "MEL" },
                { "الزراعة", "AGR" },
                { "التدريب", "TRN" },
                { "الشكاوى والامتثال", "CMP" }
            };

            return codeMap.ContainsKey(sectorName) ? codeMap[sectorName] : "GEN";
        }

        /// <summary>
        /// الحصول على بيانات الضباط الجدد
        /// </summary>
        private List<Officer> GetNewOfficersData(Dictionary<string, int> sectorMap)
        {
            return new List<Officer>
            {
                new Officer { Name = "محمد عبدالعزيز مقبل عزيز", Rank = "ضابط مشاريع", Code = "130", SectorId = GetSectorId("التعليم", sectorMap), PhoneNumber = "777407640", CardNumber = "17010026546", CardType = "شخصية", IsActive = true },
                new Officer { Name = "جمال علي علي عبدالله الفاطمي", Rank = "ضابط مشاريع", Code = "106", SectorId = GetSectorId("الصحة والحماية الاجتماعية", sectorMap), PhoneNumber = "770722006", CardNumber = "17010097208", CardType = "شخصية", IsActive = true },
                new Officer { Name = "احمد صالح احمد حيمد", Rank = "ضابط مشاريع", Code = "101", SectorId = GetSectorId("الصحة والحماية الاجتماعية", sectorMap), PhoneNumber = "774438120", CardNumber = "17010097208", CardType = "شخصية", IsActive = true },
                new Officer { Name = "علي علي احمد العمدي", Rank = "سكرتاريه التغذية", Code = "123", SectorId = GetSectorId("الصحة والحماية الاجتماعية", sectorMap), PhoneNumber = "772328123", CardNumber = "17010044025", CardType = "شخصية", IsActive = true },
                new Officer { Name = "عبدالوهاب علي اسماعيل الخولاني", Rank = "", Code = "117", SectorId = GetSectorId("الطرق", sectorMap), PhoneNumber = "", CardNumber = "", CardType = "", IsActive = true },
                new Officer { Name = "بشير عبده عزيز عبدالوهاب", Rank = "ضابط مشاريع", Code = "105", SectorId = GetSectorId("النقد مقابل العمل", sectorMap), PhoneNumber = "777595467", CardNumber = "1701005845", CardType = "شخصية", IsActive = true },
                new Officer { Name = "غلاب عبدالاله علي عذبة", Rank = "ضابط مشاريع", Code = "125", SectorId = GetSectorId("النقد مقابل العمل", sectorMap), PhoneNumber = "774113081", CardNumber = "17010017329", CardType = "شخصيىة", IsActive = true },
                new Officer { Name = "عبدالله علي ناصر الاضرعي", Rank = "ضابط مجتمعي", Code = "114", SectorId = GetSectorId("النقد مقابل العمل", sectorMap), PhoneNumber = "771239969", CardNumber = "17010044266", CardType = "شخصية", IsActive = true },
                new Officer { Name = "بدر احمد صالح المبارزي", Rank = "محاسب", Code = "104", SectorId = GetSectorId("النقد مقابل العمل", sectorMap), PhoneNumber = "771015656", CardNumber = "17010001042", CardType = "شخصية", IsActive = true },
                new Officer { Name = "محمد حمود عبده الوقاحي", Rank = "ضابط مجتمعي", Code = "129", SectorId = GetSectorId("النقد مقابل العمل", sectorMap), PhoneNumber = "777821344", CardNumber = "17010196538", CardType = "شخصية", IsActive = true },
                new Officer { Name = "علي عبدالغني محمد الحرازي", Rank = "ضابط مشاريع", Code = "122", SectorId = GetSectorId("المياه والبيئة", sectorMap), PhoneNumber = "", CardNumber = "", CardType = "", IsActive = true },
                new Officer { Name = "عدنان علوي علي السنباني", Rank = "ضابط مشاريع", Code = "118", SectorId = GetSectorId("المياه والبيئة", sectorMap), PhoneNumber = "771653316", CardNumber = "01010149503", CardType = "شخصية", IsActive = true },
                new Officer { Name = "اسمهان صالح حسين الفقية", Rank = "ضابط مشاريع", Code = "103", SectorId = GetSectorId("المياه والبيئة", sectorMap), PhoneNumber = "772064728", CardNumber = "17000004388", CardType = "شخصية", IsActive = true },
                new Officer { Name = "اسامه احمد سعيد فرحان", Rank = "ضابط محاسبي", Code = "102", SectorId = GetSectorId("المياه والبيئة", sectorMap), PhoneNumber = "777453825", CardNumber = "17010024414", CardType = "شخصية", IsActive = true },
                new Officer { Name = "يحيى عبدالوهاب عبدالله يحيى الهندي", Rank = "محاسب", Code = "135", SectorId = GetSectorId("المياه والبيئة", sectorMap), PhoneNumber = "771360366", CardNumber = "17010130985", CardType = "شخصية", IsActive = true },
                new Officer { Name = "علي احمد محمد عمران", Rank = "ضابط المراقبة والتقييم", Code = "121", SectorId = GetSectorId("المراقبة والتقييم", sectorMap), PhoneNumber = "", CardNumber = "", CardType = "", IsActive = true },
                new Officer { Name = "ساميه احمد ناصر عوضه", Rank = "ضابط مالي", Code = "110", SectorId = GetSectorId("الصحة والحماية الاجتماعية", sectorMap), PhoneNumber = "771929964", CardNumber = "17000005954", CardType = "شخصية", IsActive = true },
                new Officer { Name = "محمد عبدالله اسماعيل الثلاياء", Rank = "ضابط الشكاوى", Code = "131", SectorId = GetSectorId("الصحة والحماية الاجتماعية", sectorMap), PhoneNumber = "770959624", CardNumber = "17010015075", CardType = "شخصية", IsActive = true },
                new Officer { Name = "عبدالله نعمان عبدالقادر علي", Rank = "ضابط مشاريع", Code = "116", SectorId = GetSectorId("الزراعة", sectorMap), PhoneNumber = "771890930", CardNumber = "01010201147", CardType = "شخصية", IsActive = true },
                new Officer { Name = "عادل احمد عبدالله السوداني", Rank = "ضابط مشاريع", Code = "112", SectorId = GetSectorId("الزراعة", sectorMap), PhoneNumber = "771732558", CardNumber = "12010000411", CardType = "شخصية", IsActive = true },
                new Officer { Name = "فردوس امين عبده القرشي", Rank = "ضابط مجتمعي", Code = "126", SectorId = GetSectorId("الزراعة", sectorMap), PhoneNumber = "777075083", CardNumber = "17000004450", CardType = "شخصية", IsActive = true },
                new Officer { Name = "حميد عبدالله علي محمد السمهري", Rank = "ضابط مشاريع", Code = "108", SectorId = GetSectorId("الزراعة", sectorMap), PhoneNumber = "777368837", CardNumber = "17010057649", CardType = "شخصية", IsActive = true },
                new Officer { Name = "نظير عبدالباري احمد الحصيني", Rank = "ضابط مشاريع", Code = "134", SectorId = GetSectorId("الزراعة", sectorMap), PhoneNumber = "7777777", CardNumber = "", CardType = "", IsActive = true },
                new Officer { Name = "صفوان محمد يحيى النوفي", Rank = "ضابط محاسبي", Code = "111", SectorId = GetSectorId("الزراعة", sectorMap), PhoneNumber = "777237387", CardNumber = "17010170327", CardType = "شخصية", IsActive = true },
                new Officer { Name = "عصام علي محمود العلفي", Rank = "ضابط مشاريع", Code = "120", SectorId = GetSectorId("الزراعة", sectorMap), PhoneNumber = "777174045", CardNumber = "00110008645", CardType = "شخصية", IsActive = true },
                new Officer { Name = "عمرو حميد غانم محسن الحمراء", Rank = "ضابط مشاريع", Code = "124", SectorId = GetSectorId("الصحة والحماية الاجتماعية", sectorMap), PhoneNumber = "773389033", CardNumber = "5010126030", CardType = "شخصية", IsActive = true },
                new Officer { Name = "عبدالله محمد احمد الأسود", Rank = "ضابط مشاريع", Code = "115", SectorId = GetSectorId("النقد مقابل العمل", sectorMap), PhoneNumber = "777530929", CardNumber = "17010054161", CardType = "شخصية", IsActive = true },
                new Officer { Name = "فهد فرحان محمد عبدالله القريشي", Rank = "ضابط مشاريع", Code = "127", SectorId = GetSectorId("التعليم", sectorMap), PhoneNumber = "771743512", CardNumber = "17010018462", CardType = "شخصية", IsActive = true },
                new Officer { Name = "عادل عبدالله سفيان كليب", Rank = "محاسب المشروع", Code = "113", SectorId = GetSectorId("التدريب", sectorMap), PhoneNumber = "777621190", CardNumber = "17010018334", CardType = "شخصية", IsActive = true },
                new Officer { Name = "احمد رزق الله محمد الحاج", Rank = "ضابط التعاقدات", Code = "100", SectorId = GetSectorId("الصحة والحماية الاجتماعية", sectorMap), PhoneNumber = "777796816", CardNumber = "00110006478", CardType = "شخصية", IsActive = true },
                new Officer { Name = "محمد احمد محمد الرعوي", Rank = "ضابط الفنية", Code = "128", SectorId = GetSectorId("التعليم", sectorMap), PhoneNumber = "777368036", CardNumber = "17010043873", CardType = "شخصية", IsActive = true },
                new Officer { Name = "عدنان محمد علي ناصر النجحي", Rank = "نائب مدير الفرع", Code = "119", SectorId = GetSectorId("الزراعة", sectorMap), PhoneNumber = "777600528", CardNumber = "17010024137", CardType = "شخصية", IsActive = true },
                new Officer { Name = "حسن عبدالله زيد عمران", Rank = "استشاري", Code = "107", SectorId = GetSectorId("الزراعة", sectorMap), PhoneNumber = "770124258", CardNumber = "17010113585", CardType = "شخصيه", IsActive = true },
                new Officer { Name = "خالد محمد احمد الاحصب", Rank = "ضابط مشاريع", Code = "109", SectorId = GetSectorId("الصحة والحماية الاجتماعية", sectorMap), PhoneNumber = "777902909", CardNumber = "17010052406", CardType = "شخصية", IsActive = true },

                // الضباط المكررين في قطاعات أخرى
                new Officer { Name = "محمد حمود عبده الوقاحي", Rank = "ضابط مجتمعي", Code = "129B", SectorId = GetSectorId("الطرق", sectorMap), PhoneNumber = "777821344", CardNumber = "17010196538", CardType = "شخصية", IsActive = true },
                new Officer { Name = "فردوس امين عبده القرشي", Rank = "ضابط مجتمعي", Code = "126B", SectorId = GetSectorId("الطرق", sectorMap), PhoneNumber = "777075083", CardNumber = "17000004450", CardType = "شخصية", IsActive = true },
                new Officer { Name = "اسمهان صالح حسين الفقية", Rank = "ضابط مشاريع", Code = "103B", SectorId = GetSectorId("الشكاوى والامتثال", sectorMap), PhoneNumber = "772064728", CardNumber = "17000004388", CardType = "شخصية", IsActive = true }
            };
        }

        /// <summary>
        /// الحصول على معرف القطاع
        /// </summary>
        private int GetSectorId(string sectorName, Dictionary<string, int> sectorMap)
        {
            return sectorMap.ContainsKey(sectorName) ? sectorMap[sectorName] : 1;
        }
    }
}
