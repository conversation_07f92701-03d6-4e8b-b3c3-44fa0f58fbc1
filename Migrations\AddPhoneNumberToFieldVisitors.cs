using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using System;
using System.Threading.Tasks;

namespace DriverManagementSystem.Migrations
{
    public static class AddPhoneNumberToFieldVisitors
    {
        public static async Task ApplyAsync(ApplicationDbContext context)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء تطبيق migration إضافة عمود PhoneNumber لجدول FieldVisitors");

                // التحقق من وجود العمود أولاً
                var checkColumnSql = @"
                    SELECT COUNT(*) 
                    FROM pragma_table_info('FieldVisitors') 
                    WHERE name = 'PhoneNumber'";

                var columnExists = await context.Database.ExecuteSqlRawAsync(checkColumnSql);
                
                if (columnExists == 0)
                {
                    // إضافة عمود PhoneNumber
                    var addPhoneNumberSql = @"
                        ALTER TABLE FieldVisitors
                        ADD COLUMN PhoneNumber TEXT DEFAULT ''";

                    await context.Database.ExecuteSqlRawAsync(addPhoneNumberSql);
                    System.Diagnostics.Debug.WriteLine("✅ تم إضافة عمود PhoneNumber لجدول FieldVisitors");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ عمود PhoneNumber موجود مسبقاً في جدول FieldVisitors");
                }

                System.Diagnostics.Debug.WriteLine("✅ تم تطبيق migration إضافة عمود PhoneNumber بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تطبيق migration إضافة عمود PhoneNumber: {ex.Message}");
                
                // لا نرمي الخطأ إذا كان العمود موجود مسبقاً
                if (!ex.Message.Contains("duplicate column name") && 
                    !ex.Message.Contains("already exists"))
                {
                    throw;
                }
            }
        }
    }
}
