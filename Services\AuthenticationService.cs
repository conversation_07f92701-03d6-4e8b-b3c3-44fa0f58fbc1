using System.Threading.Tasks;
using System;
using DriverManagementSystem.Models;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.Services
{
    public interface IAuthenticationService
    {
        Task<User?> LoginAsync(string username, string password);
        Task<bool> LogoutAsync();
        User? CurrentUser { get; }
    }

    public class AuthenticationService : IAuthenticationService
    {
        private readonly IUserService _userService;

        public User? CurrentUser { get; private set; }

        public AuthenticationService()
        {
            var context = new ApplicationDbContext();
            _userService = new UserService(context);
        }

        public async Task<User?> LoginAsync(string username, string password)
        {
            try
            {
                // Initialize database if needed
                var context = new ApplicationDbContext();
                await context.InitializeDatabaseAsync();

                // Validate user credentials
                var isValid = await _userService.ValidateUserAsync(username, password);
                if (isValid)
                {
                    CurrentUser = await _userService.GetUserByUsernameAsync(username);
                    if (CurrentUser != null)
                    {
                        // Update last login time
                        CurrentUser.LastLoginDate = DateTime.Now;
                        await _userService.UpdateUserAsync(CurrentUser);
                        return CurrentUser;
                    }
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> LogoutAsync()
        {
            await Task.Delay(500);
            CurrentUser = null;
            return true;
        }
    }
}
