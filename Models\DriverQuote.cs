using System;
using System.ComponentModel;

namespace DriverManagementSystem.Models
{
    /// <summary>
    /// نموذج عرض الأسعار المقدم من السائقين - تصميم احترافي
    /// </summary>
    public class DriverQuote : INotifyPropertyChanged
    {
        private bool _isSelected;
        private decimal _quotedPrice;
        private int _quotedDays;
        private string _notes = string.Empty;

        public int Id { get; set; }
        public int DriverId { get; set; }
        public string DriverName { get; set; } = string.Empty;
        public string DriverCode { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string VehicleType { get; set; } = string.Empty;
        public string VehicleNumber { get; set; } = string.Empty;

        /// <summary>
        /// السعر المقدم من السائق
        /// </summary>
        public decimal QuotedPrice
        {
            get => _quotedPrice;
            set
            {
                if (_quotedPrice != value)
                {
                    _quotedPrice = value;
                    OnPropertyChanged(nameof(QuotedPrice));
                    OnPropertyChanged(nameof(FormattedPrice));
                }
            }
        }

        /// <summary>
        /// عدد الأيام المقترحة
        /// </summary>
        public int QuotedDays
        {
            get => _quotedDays;
            set
            {
                if (_quotedDays != value)
                {
                    _quotedDays = value;
                    OnPropertyChanged(nameof(QuotedDays));
                }
            }
        }

        /// <summary>
        /// ملاحظات العرض
        /// </summary>
        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value ?? string.Empty;
                    OnPropertyChanged(nameof(Notes));
                }
            }
        }

        /// <summary>
        /// تاريخ تقديم العرض
        /// </summary>
        public DateTime QuoteDate { get; set; } = DateTime.Now;

        /// <summary>
        /// حالة العرض
        /// </summary>
        public QuoteStatus Status { get; set; } = QuoteStatus.Pending;

        /// <summary>
        /// هل تم اختيار هذا السائق
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged(nameof(IsSelected));
                }
            }
        }

        /// <summary>
        /// السعر منسق للعرض
        /// </summary>
        public string FormattedPrice => $"{QuotedPrice:N0} ريال";

        /// <summary>
        /// حالة العرض منسقة للعرض
        /// </summary>
        public string StatusText => Status switch
        {
            QuoteStatus.Pending => "في الانتظار",
            QuoteStatus.Accepted => "مقبول",
            QuoteStatus.Rejected => "مرفوض",
            QuoteStatus.UnderReview => "قيد المراجعة",
            _ => "غير محدد"
        };

        /// <summary>
        /// لون حالة العرض
        /// </summary>
        public string StatusColor => Status switch
        {
            QuoteStatus.Pending => "#FFA500",
            QuoteStatus.Accepted => "#28A745",
            QuoteStatus.Rejected => "#DC3545",
            QuoteStatus.UnderReview => "#007BFF",
            _ => "#6C757D"
        };

        // تنفيذ INotifyPropertyChanged
        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// حالات عرض الأسعار
    /// </summary>
    public enum QuoteStatus
    {
        Pending = 0,        // في الانتظار
        UnderReview = 1,    // قيد المراجعة
        Accepted = 2,       // مقبول
        Rejected = 3        // مرفوض
    }
}
