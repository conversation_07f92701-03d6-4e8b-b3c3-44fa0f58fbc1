﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SFDSystem.Migrations
{
    /// <inheritdoc />
    public partial class RemoveUniqueConstraintFromFieldVisitProjects : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "SelectedDrivers",
                table: "FieldVisits",
                type: "TEXT",
                maxLength: 2000,
                nullable: true);

            migrationBuilder.CreateTable(
                name: "FieldVisitProjects",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    FieldVisitId = table.Column<int>(type: "INTEGER", nullable: false),
                    ProjectId = table.Column<int>(type: "INTEGER", nullable: true),
                    ProjectNumber = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    ProjectName = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    DisplayOrder = table.Column<int>(type: "INTEGER", nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FieldVisitProjects", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FieldVisitProjects_FieldVisits_FieldVisitId",
                        column: x => x.FieldVisitId,
                        principalTable: "FieldVisits",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_FieldVisitProjects_Projects_ProjectId",
                        column: x => x.ProjectId,
                        principalTable: "Projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_FieldVisitProjects_FieldVisitId",
                table: "FieldVisitProjects",
                column: "FieldVisitId");

            migrationBuilder.CreateIndex(
                name: "IX_FieldVisitProjects_ProjectId",
                table: "FieldVisitProjects",
                column: "ProjectId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "FieldVisitProjects");

            migrationBuilder.DropColumn(
                name: "SelectedDrivers",
                table: "FieldVisits");
        }
    }
}
