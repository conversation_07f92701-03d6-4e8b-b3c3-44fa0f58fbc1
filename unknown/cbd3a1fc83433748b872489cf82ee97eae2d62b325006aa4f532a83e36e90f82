using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Input;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using DriverManagementSystem.Models;
using DriverManagementSystem.ViewModels;

namespace DriverManagementSystem.Views
{
    public partial class PowerfulMessageDocumentationWindow : Window
    {
        public PowerfulMessageDocumentationWindow()
        {
            InitializeComponent();
            // إنشاء ViewModel افتراضي بدون زيارة محددة
            DataContext = new PowerfulMessageDocumentationViewModel(null);
        }

        public PowerfulMessageDocumentationWindow(FieldVisit selectedVisit) : this()
        {
            DataContext = new PowerfulMessageDocumentationViewModel(selectedVisit);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }



        private void Image_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // هذا المعالج لم يعد مستخدماً - تم استبداله بالمعاينة الجانبية
        }

        private void SideThumbnail_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border && border.DataContext is string imagePath)
            {
                try
                {
                    // تحميل الصورة بحجم محدود لتجنب استهلاك الذاكرة
                    var bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(imagePath);
                    bitmap.DecodePixelWidth = 600; // حد أقصى للعرض
                    bitmap.DecodePixelHeight = 400; // حد أقصى للارتفاع
                    bitmap.CacheOption = BitmapCacheOption.OnLoad;
                    bitmap.EndInit();
                    bitmap.Freeze(); // تحسين الأداء

                    // عرض الصورة في المعاينة الجانبية الكبيرة
                    LargePreviewImage.Source = bitmap;
                    LargePreviewName.Text = System.IO.Path.GetFileName(imagePath);

                    // إظهار المعاينة وإخفاء الحالة الفارغة
                    LargePreviewBorder.Visibility = Visibility.Visible;
                    LargePreviewFooter.Visibility = Visibility.Visible;
                    EmptyPreviewState.Visibility = Visibility.Collapsed;

                    // حفظ مسار الصورة الحالية للاستخدام في الوظائف الأخرى
                    LargePreviewImage.Tag = imagePath;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في عرض الصورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);

                    // إخفاء المعاينة في حالة الخطأ
                    LargePreviewBorder.Visibility = Visibility.Collapsed;
                    LargePreviewFooter.Visibility = Visibility.Collapsed;
                    EmptyPreviewState.Visibility = Visibility.Visible;
                }
            }
        }

        private void CloseImagePreview_Click(object sender, RoutedEventArgs e)
        {
            ImagePreviewOverlay.Visibility = Visibility.Collapsed;
        }

        private void ZoomImage_Click(object sender, RoutedEventArgs e)
        {
            if (LargePreviewImage.Tag is string imagePath)
            {
                try
                {
                    // عرض الصورة في نافذة التكبير
                    var fullBitmap = new BitmapImage(new Uri(imagePath));
                    PreviewImage.Source = fullBitmap;
                    PreviewImageName.Text = System.IO.Path.GetFileName(imagePath);
                    ImagePreviewOverlay.Visibility = Visibility.Visible;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تكبير الصورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void SaveImage_Click(object sender, RoutedEventArgs e)
        {
            if (LargePreviewImage.Tag is string imagePath)
            {
                try
                {
                    var saveDialog = new Microsoft.Win32.SaveFileDialog
                    {
                        Title = "حفظ الصورة",
                        Filter = "صور PNG|*.png|صور JPEG|*.jpg|جميع الملفات|*.*",
                        FileName = System.IO.Path.GetFileNameWithoutExtension(imagePath) + "_copy"
                    };

                    if (saveDialog.ShowDialog() == true)
                    {
                        System.IO.File.Copy(imagePath, saveDialog.FileName, true);
                        MessageBox.Show("تم حفظ الصورة بنجاح!", "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حفظ الصورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
    }
}
