using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace DriverManagementSystem.Models
{
    public class Officer : INotifyPropertyChanged
    {
        private int _id;
        private string _name = string.Empty;
        private string _rank = string.Empty;
        private string _phoneNumber = string.Empty;
        private string _cardNumber = string.Empty;
        private string _cardType = string.Empty;
        private string _code = string.Empty;
        private int _sectorId;
        private string _sectorName = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private bool _isActive = true;

        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public string Rank
        {
            get => _rank;
            set => SetProperty(ref _rank, value);
        }

        public string PhoneNumber
        {
            get => _phoneNumber;
            set => SetProperty(ref _phoneNumber, value);
        }

        public string CardNumber
        {
            get => _cardNumber;
            set => SetProperty(ref _cardNumber, value);
        }

        public string CardType
        {
            get => _cardType;
            set => SetProperty(ref _cardType, value);
        }

        public string Code
        {
            get => _code;
            set => SetProperty(ref _code, value);
        }

        public int SectorId
        {
            get => _sectorId;
            set => SetProperty(ref _sectorId, value);
        }

        public string SectorName
        {
            get => _sectorName;
            set => SetProperty(ref _sectorName, value);
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set => SetProperty(ref _createdAt, value);
        }

        public bool IsActive
        {
            get => _isActive;
            set => SetProperty(ref _isActive, value);
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (!Equals(field, value))
            {
                field = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
            }
        }
    }
}
