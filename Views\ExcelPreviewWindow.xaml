<Window x:Class="DriverManagementSystem.Views.ExcelPreviewWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="معاينة بيانات Excel" Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- Modern Button Style like Excel Import with Icons -->
        <Style x:Key="FormalButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="45"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="6"
                               Padding="{TemplateBinding Padding}"
                               x:Name="border">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock x:Name="IconText" Text="{TemplateBinding Tag}"
                                          FontSize="16" FontWeight="Bold"
                                          Foreground="{TemplateBinding Foreground}"
                                          Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="border" Property="Opacity" Value="0.5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="TextBlock" x:Key="HeaderStyle">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#2E3440"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>

        <Style TargetType="TextBlock" x:Key="ValueStyle">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#4C566A"/>
            <Setter Property="Margin" Value="0,2"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#E3F2FD" CornerRadius="8" Padding="15" Margin="0,0,0,15">
            <StackPanel>
                <TextBlock Text="📊 معاينة البيانات المستوردة من Excel" 
                          FontSize="16" FontWeight="Bold" Foreground="#1976D2"/>
                <TextBlock Text="تحقق من صحة البيانات قبل الاستيراد النهائي" 
                          FontSize="12" Foreground="#424242" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- Visit Data Section -->
                <Border Background="#F8F9FA" CornerRadius="8" Padding="15" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="📋 بيانات الزيارة الأساسية" Style="{StaticResource HeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="رقم الزيارة:" FontWeight="Bold"/>
                                <TextBlock Text="{Binding VisitData.VisitFormNumber}" Style="{StaticResource ValueStyle}"/>
                                
                                <TextBlock Text="عدد الأيام:" FontWeight="Bold" Margin="0,10,0,0"/>
                                <TextBlock Text="{Binding VisitData.FieldDaysCount}" Style="{StaticResource ValueStyle}"/>
                                
                                <TextBlock Text="تاريخ البداية:" FontWeight="Bold" Margin="0,10,0,0"/>
                                <TextBlock Text="{Binding VisitData.StartDate, StringFormat=dd/MM/yyyy}" Style="{StaticResource ValueStyle}"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <TextBlock Text="مهمة النزول:" FontWeight="Bold"/>
                                <TextBlock Text="{Binding VisitData.TripPurpose}" Style="{StaticResource ValueStyle}"/>

                                <TextBlock Text="القطاع:" FontWeight="Bold" Margin="0,10,0,0"/>
                                <TextBlock Text="{Binding VisitData.Sector}" Style="{StaticResource ValueStyle}"/>

                                <TextBlock Text="القائمين بالزيارة:" FontWeight="Bold" Margin="0,10,0,0"/>
                                <TextBlock Text="{Binding VisitData.Visitors}" Style="{StaticResource ValueStyle}" TextWrapping="Wrap"/>

                                <TextBlock Text="تاريخ النهاية:" FontWeight="Bold" Margin="0,10,0,0"/>
                                <TextBlock Text="{Binding VisitData.EndDate, StringFormat=dd/MM/yyyy}" Style="{StaticResource ValueStyle}"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Projects Section -->
                <Border Background="#F0F8F0" CornerRadius="8" Padding="15" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="🏗️ المشاريع المستوردة" Style="{StaticResource HeaderStyle}"/>
                        
                        <ItemsControl ItemsSource="{Binding Projects}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="White" CornerRadius="5" Padding="10" Margin="0,0,0,5">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <TextBlock Grid.Column="0" Text="{Binding ProjectCode}" 
                                                      FontWeight="Bold" Foreground="#2E7D32" Margin="0,0,10,0"/>
                                            <TextBlock Grid.Column="1" Text="{Binding ProjectName}" 
                                                      Style="{StaticResource ValueStyle}"/>
                                            <TextBlock Grid.Column="2" Text="{Binding ProjectDays, StringFormat={}{0} أيام}" 
                                                      FontWeight="Bold" Foreground="#1976D2"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </Border>

                <!-- Itinerary Section -->
                <Border Background="#FFF8E1" CornerRadius="8" Padding="15">
                    <StackPanel>
                        <TextBlock Text="🗓️ خط السير المستورد" Style="{StaticResource HeaderStyle}"/>
                        
                        <ItemsControl ItemsSource="{Binding Itinerary}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="White" CornerRadius="5" Padding="10" Margin="0,0,0,5">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="{Binding DayNumber, StringFormat=اليوم {0}:}" 
                                                      FontWeight="Bold" Foreground="#F57C00" Margin="0,0,10,0"/>
                                            <TextBlock Text="{Binding Plan}" Style="{StaticResource ValueStyle}"/>
                                        </StackPanel>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="#F5F5F5" CornerRadius="8" Padding="25" Margin="0,15,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15">
                    <!-- زر تأكيد الاستيراد -->
                    <Button Name="ConfirmButton" Content="تأكيد الاستيراد" Tag="✓" Click="ConfirmButton_Click"
                            Background="#2E7D32" Foreground="White"
                            Padding="30,12" Margin="0,0,20,0"
                            Style="{StaticResource FormalButtonStyle}"/>

                    <!-- زر الإلغاء -->
                    <Button Name="CancelButton" Content="إلغاء" Tag="✕" Click="CancelButton_Click"
                            Background="#D32F2F" Foreground="White"
                            Padding="30,12"
                            Style="{StaticResource FormalButtonStyle}"/>
                </StackPanel>
            </Grid>
        </Border>

    </Grid>
</Window>
