@echo off
chcp 65001 >nul
echo ===============================================
echo    🚀 تشغيل نظام SFDALI - النسخة النظيفة
echo ===============================================
echo.

echo 📍 المسار: C:\Users\<USER>\Desktop\sys\SFDALI
echo 🎯 النسخة: نسخة واحدة فقط - بدون تعارض
echo.

echo 🔍 فحص النسخ الحالية...
tasklist /FI "IMAGENAME eq SFDSystem.exe" | find "SFDSystem.exe" >nul
if %ERRORLEVEL% EQU 0 (
    echo ⚠️  يوجد نسخة تعمل بالفعل
    echo 🔄 إغلاق النسخ السابقة...
    taskkill /F /IM SFDSystem.exe >nul 2>&1
    timeout /t 2 >nul
)

echo 🚀 تشغيل النسخة الجديدة...
start "" "bin\Debug\net9.0-windows\SFDSystem.exe"

echo.
echo ✅ تم تشغيل النظام بنجاح!
echo.
echo 📋 اختبار زر التكليف:
echo 1. اذهب إلى "التقارير"
echo 2. اختر زيارة من القائمة
echo 3. اضغط على زر "التكليف" 📋
echo 4. يجب أن تفتح النافذة المبسطة بدون أخطاء
echo.
echo 🗄️ زر استعادة قاعدة البيانات متاح في الشريط الجانبي
echo.

pause
