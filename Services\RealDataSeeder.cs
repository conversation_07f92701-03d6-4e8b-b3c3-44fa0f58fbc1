using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    public class RealDataSeeder
    {
        private readonly DatabaseService _dataService;

        public RealDataSeeder()
        {
            _dataService = new DatabaseService();
        }

        public async Task SeedRealDataAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🌱 بدء إضافة البيانات الحقيقية...");

                await SeedRealOfficersAsync();
                await SeedRealDriversAsync();

                System.Diagnostics.Debug.WriteLine("✅ تم إضافة جميع البيانات الحقيقية بنجاح!");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة البيانات: {ex.Message}");
            }
        }

        private async Task SeedRealOfficersAsync()
        {
            // حذف جميع الموظفين القدامى أولاً
            System.Diagnostics.Debug.WriteLine("🗑️ حذف الموظفين القدامى...");
            var existingOfficers = await _dataService.GetOfficersAsync();
            foreach (var officer in existingOfficers)
            {
                await _dataService.DeleteOfficerAsync(officer.Id);
            }
            System.Diagnostics.Debug.WriteLine($"✅ تم حذف {existingOfficers.Count} موظف قديم");

            var sectors = await _dataService.GetSectorsAsync();
            if (sectors.Count == 0)
            {
                System.Diagnostics.Debug.WriteLine("⚠️ لا توجد قطاعات لإضافة الموظفين");
                return;
            }

            // البحث عن القطاعات بالاسم
            var educationSector = sectors.Find(s => s.Name == "التعليم");
            var healthSector = sectors.Find(s => s.Name == "الصحة والحماية الاجتماعية");
            var cashSector = sectors.Find(s => s.Name == "النقد مقابل العمل");
            var waterSector = sectors.Find(s => s.Name == "المياه والبيئة");
            var monitoringSector = sectors.Find(s => s.Name == "المراقبة والتقييم");
            var agricultureSector = sectors.Find(s => s.Name == "الزراعة");
            var trainingSector = sectors.Find(s => s.Name == "التدريب");
            var roadsSector = sectors.Find(s => s.Name == "الطرق");
            var complaintsSector = sectors.Find(s => s.Name == "الشكاوى والامتثال");

            // البيانات الجديدة بالضبط كما أرسلها المستخدم
            var realOfficers = new List<Officer>
            {
                // الكود 71
                new Officer { Name = "محمد عبدالعزيز مقبل عزيز", Rank = "ضابط مشاريع", Code = "130", SectorId = educationSector?.Id ?? 4, PhoneNumber = "777407640", CardNumber = "17010026546", CardType = "شخصية", IsActive = true },

                // الكود 76
                new Officer { Name = "جمال علي علي عبدالله الفاطمي", Rank = "ضابط مشاريع", Code = "106", SectorId = healthSector?.Id ?? 10, PhoneNumber = "770722006", CardNumber = "17010097208", CardType = "شخصية", IsActive = true },

                // الكود 77
                new Officer { Name = "احمد صالح احمد حيمد", Rank = "ضابط مشاريع", Code = "101", SectorId = healthSector?.Id ?? 10, PhoneNumber = "774438120", CardNumber = "17010097208", CardType = "شخصية", IsActive = true },

                // الكود 78
                new Officer { Name = "علي علي احمد العمدي", Rank = "سكرتاريه التغذية", Code = "123", SectorId = healthSector?.Id ?? 10, PhoneNumber = "772328123", CardNumber = "17010044025", CardType = "شخصية", IsActive = true },

                // الكود 79
                new Officer { Name = "عبدالوهاب علي اسماعيل الخولاني", Rank = "", Code = "117", SectorId = roadsSector?.Id ?? 11, PhoneNumber = "", CardNumber = "", CardType = "", IsActive = true },

                // الكود 83
                new Officer { Name = "بشير عبده عزيز عبدالوهاب", Rank = "ضابط مشاريع", Code = "105", SectorId = cashSector?.Id ?? 15, PhoneNumber = "777595467", CardNumber = "**********", CardType = "شخصية", IsActive = true },

                // الكود 84
                new Officer { Name = "غلاب عبدالاله علي عذبة", Rank = "ضابط مشاريع", Code = "125", SectorId = cashSector?.Id ?? 15, PhoneNumber = "774113081", CardNumber = "17010017329", CardType = "شخصيىة", IsActive = true },

                // الكود 85
                new Officer { Name = "عبدالله علي ناصر الاضرعي", Rank = "ضابط مجتمعي", Code = "114", SectorId = cashSector?.Id ?? 15, PhoneNumber = "771239969", CardNumber = "17010044266", CardType = "شخصية", IsActive = true },

                // الكود 86
                new Officer { Name = "بدر احمد صالح المبارزي", Rank = "محاسب", Code = "104", SectorId = cashSector?.Id ?? 15, PhoneNumber = "771015656", CardNumber = "17010001042", CardType = "شخصية", IsActive = true },

                // الكود 88
                new Officer { Name = "محمد حمود عبده الوقاحي", Rank = "ضابط مجتمعي", Code = "129", SectorId = cashSector?.Id ?? 15, PhoneNumber = "777821344", CardNumber = "17010196538", CardType = "شخصية", IsActive = true },

                // الكود 89
                new Officer { Name = "علي عبدالغني محمد الحرازي", Rank = "ضابط مشاريع", Code = "122", SectorId = waterSector?.Id ?? 14, PhoneNumber = "", CardNumber = "", CardType = "", IsActive = true },

                // الكود 90
                new Officer { Name = "عدنان علوي علي السنباني", Rank = "ضابط مشاريع", Code = "118", SectorId = waterSector?.Id ?? 14, PhoneNumber = "771653316", CardNumber = "01010149503", CardType = "شخصية", IsActive = true },

                // الكود 91
                new Officer { Name = "اسمهان صالح حسين الفقية", Rank = "ضابط مشاريع", Code = "103", SectorId = waterSector?.Id ?? 14, PhoneNumber = "772064728", CardNumber = "17000004388", CardType = "شخصية", IsActive = true },

                // الكود 92
                new Officer { Name = "اسامه احمد سعيد فرحان", Rank = "ضابط محاسبي", Code = "102", SectorId = waterSector?.Id ?? 14, PhoneNumber = "777453825", CardNumber = "17010024414", CardType = "شخصية", IsActive = true },

                // الكود 93
                new Officer { Name = "يحيى عبدالوهاب عبدالله يحيى الهندي", Rank = "محاسب", Code = "135", SectorId = waterSector?.Id ?? 14, PhoneNumber = "771360366", CardNumber = "17010130985", CardType = "شخصية", IsActive = true }
            };

            foreach (var officer in realOfficers)
            {
                await _dataService.AddOfficerAsync(officer);
            }

            // باقي البيانات الجديدة
            var moreOfficers = new List<Officer>
            {
                // الكود 100
                new Officer { Name = "علي احمد محمد عمران", Rank = "ضابط المراقبة والتقييم", Code = "121", SectorId = monitoringSector?.Id ?? 13, PhoneNumber = "", CardNumber = "", CardType = "", IsActive = true },

                // الكود 106
                new Officer { Name = "ساميه احمد ناصر عوضه", Rank = "ضابط مالي", Code = "110", SectorId = healthSector?.Id ?? 10, PhoneNumber = "771929964", CardNumber = "17000005954", CardType = "شخصية", IsActive = true },

                // الكود 117
                new Officer { Name = "محمد عبدالله اسماعيل الثلاياء", Rank = "ضابط الشكاوى", Code = "131", SectorId = healthSector?.Id ?? 10, PhoneNumber = "770959624", CardNumber = "17010015075", CardType = "شخصية", IsActive = true },

                // الكود 119
                new Officer { Name = "عبدالله نعمان عبدالقادر علي", Rank = "ضابط مشاريع", Code = "116", SectorId = agricultureSector?.Id ?? 7, PhoneNumber = "771890930", CardNumber = "01010201147", CardType = "شخصية", IsActive = true },

                // الكود 121
                new Officer { Name = "عادل احمد عبدالله السوداني", Rank = "ضابط مشاريع", Code = "112", SectorId = agricultureSector?.Id ?? 7, PhoneNumber = "771732558", CardNumber = "12010000411", CardType = "شخصية", IsActive = true },

                // الكود 122
                new Officer { Name = "فردوس امين عبده القرشي", Rank = "ضابط مجتمعي", Code = "126", SectorId = agricultureSector?.Id ?? 7, PhoneNumber = "777075083", CardNumber = "17000004450", CardType = "شخصية", IsActive = true },

                // الكود 124
                new Officer { Name = "حميد عبدالله علي محمد السمهري", Rank = "ضابط مشاريع", Code = "108", SectorId = agricultureSector?.Id ?? 7, PhoneNumber = "777368837", CardNumber = "17010057649", CardType = "شخصية", IsActive = true },

                // الكود 125
                new Officer { Name = "نظير عبدالباري احمد الحصيني", Rank = "ضابط مشاريع", Code = "134", SectorId = agricultureSector?.Id ?? 7, PhoneNumber = "7777777", CardNumber = "", CardType = "", IsActive = true },

                // الكود 126
                new Officer { Name = "صفوان محمد يحيى النوفي", Rank = "ضابط محاسبي", Code = "111", SectorId = agricultureSector?.Id ?? 7, PhoneNumber = "777237387", CardNumber = "17010170327", CardType = "شخصية", IsActive = true },

                // الكود 137
                new Officer { Name = "عصام علي محمود العلفي", Rank = "ضابط مشاريع", Code = "120", SectorId = agricultureSector?.Id ?? 7, PhoneNumber = "777174045", CardNumber = "00110008645", CardType = "شخصية", IsActive = true },

                // الكود 138
                new Officer { Name = "عمرو حميد غانم محسن الحمراء", Rank = "ضابط مشاريع", Code = "124", SectorId = healthSector?.Id ?? 10, PhoneNumber = "773389033", CardNumber = "**********", CardType = "شخصية", IsActive = true },

                // الكود 143
                new Officer { Name = "عبدالله محمد احمد الأسود", Rank = "ضابط مشاريع", Code = "115", SectorId = cashSector?.Id ?? 15, PhoneNumber = "777530929", CardNumber = "17010054161", CardType = "شخصية", IsActive = true },

                // الكود 152
                new Officer { Name = "فهد فرحان محمد عبدالله القريشي", Rank = "ضابط مشاريع", Code = "127", SectorId = educationSector?.Id ?? 4, PhoneNumber = "771743512", CardNumber = "17010018462", CardType = "شخصية", IsActive = true },

                // الكود 155
                new Officer { Name = "عادل عبدالله سفيان كليب", Rank = "محاسب المشروع", Code = "113", SectorId = trainingSector?.Id ?? 2, PhoneNumber = "777621190", CardNumber = "17010018334", CardType = "شخصية", IsActive = true },

                // الكود 158
                new Officer { Name = "احمد رزق الله محمد الحاج", Rank = "ضابط التعاقدات", Code = "100", SectorId = healthSector?.Id ?? 10, PhoneNumber = "777796816", CardNumber = "00110006478", CardType = "شخصية", IsActive = true },

                // الكود 162
                new Officer { Name = "محمد احمد محمد الرعوي", Rank = "ضابط الفنية", Code = "128", SectorId = educationSector?.Id ?? 4, PhoneNumber = "777368036", CardNumber = "17010043873", CardType = "شخصية", IsActive = true },

                // الكود 167
                new Officer { Name = "عدنان محمد علي ناصر النجحي", Rank = "نائب مدير الفرع", Code = "119", SectorId = agricultureSector?.Id ?? 7, PhoneNumber = "777600528", CardNumber = "17010024137", CardType = "شخصية", IsActive = true },

                // الكود 172
                new Officer { Name = "حسن عبدالله زيد عمران", Rank = "استشاري", Code = "107", SectorId = agricultureSector?.Id ?? 7, PhoneNumber = "770124258", CardNumber = "17010113585", CardType = "شخصيه", IsActive = true },

                // الكود 180
                new Officer { Name = "خالد محمد احمد الاحصب", Rank = "ضابط مشاريع", Code = "109", SectorId = healthSector?.Id ?? 10, PhoneNumber = "777902909", CardNumber = "17010052406", CardType = "شخصية", IsActive = true },

                // الكود 182 - الطرق
                new Officer { Name = "محمد حمود عبده الوقاحي", Rank = "ضابط مجتمعي", Code = "129", SectorId = roadsSector?.Id ?? 11, PhoneNumber = "777821344", CardNumber = "17010196538", CardType = "شخصية", IsActive = true },

                // الكود 184 - الطرق
                new Officer { Name = "فردوس امين عبده القرشي", Rank = "ضابط مجتمعي", Code = "126", SectorId = roadsSector?.Id ?? 11, PhoneNumber = "777075083", CardNumber = "17000004450", CardType = "شخصية", IsActive = true },

                // الكود 187 - الشكاوى والامتثال
                new Officer { Name = "اسمهان صالح حسين الفقية", Rank = "ضابط مشاريع", Code = "103", SectorId = complaintsSector?.Id ?? 9, PhoneNumber = "772064728", CardNumber = "17000004388", CardType = "شخصية", IsActive = true }
            };

            // إضافة البيانات الإضافية
            foreach (var officer in moreOfficers)
            {
                await _dataService.AddOfficerAsync(officer);
            }

            // حساب العدد الإجمالي
            var totalCount = realOfficers.Count + moreOfficers.Count;
            System.Diagnostics.Debug.WriteLine($"👥 تم إضافة {totalCount} موظف حقيقي");
        }

        private async Task SeedRealDriversAsync()
        {
            // حذف جميع السائقين والسيارات القدامى أولاً
            System.Diagnostics.Debug.WriteLine("🗑️ حذف السائقين والسيارات القدامى...");
            var existingDrivers = await _dataService.GetDriversAsync();
            foreach (var driver in existingDrivers)
            {
                await _dataService.DeleteDriverAsync(driver.Id);
            }
            System.Diagnostics.Debug.WriteLine($"✅ تم حذف {existingDrivers.Count} سائق قديم");

            // حذف جميع السيارات القديمة
            var existingVehicles = await _dataService.GetVehiclesAsync();
            foreach (var vehicle in existingVehicles)
            {
                await _dataService.DeleteVehicleAsync(vehicle.Id);
            }
            System.Diagnostics.Debug.WriteLine($"✅ تم حذف {existingVehicles.Count} سيارة قديمة");

            var realDrivers = new List<Driver>
            {
                new Driver { Name = "إبراهيم عبدالله الطيب الطيري", DriverCode = "DR102", PhoneNumber = "770137382", CardNumber = "17010087881", CardType = "شخصية", CardIssueDate = DateTime.Parse("2021-12-29"), LicenseNumber = "9-17708", LicenseIssueDate = DateTime.Parse("2022-04-06"), VehicleNumber = "9-18631", VehicleType = "فورشنال", VehicleColor = "فضي", VehicleModel = "2009", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "اكرم زيد محمد الخربي", DriverCode = "DR084", PhoneNumber = "778854986", CardNumber = "17010350457", CardType = "شخصية", CardIssueDate = DateTime.Parse("2024-09-07"), LicenseNumber = "-", LicenseIssueDate = DateTime.Parse("2024-09-07"), VehicleNumber = "9-3860", VehicleType = "حافلة", VehicleColor = "ابيض", VehicleModel = "2004", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "بشير ابراهيم علي الحجي", DriverCode = "DR065", PhoneNumber = "771633491", CardNumber = "17010002740", CardType = "شخصية", CardIssueDate = DateTime.Parse("2014-11-11"), LicenseNumber = "9-21514", LicenseIssueDate = DateTime.Parse("2023-01-04"), VehicleNumber = "9-29959", VehicleType = "هيلوكس", VehicleColor = "ابيض", VehicleModel = "2013", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "حسين عبده احمد صالح السباعي", DriverCode = "DR094", PhoneNumber = "773645381", CardNumber = "15010165489", CardType = "شخصية", CardIssueDate = DateTime.Parse("2024-10-25"), LicenseNumber = "1-51841", LicenseIssueDate = DateTime.Parse("2022-02-09"), VehicleNumber = "7-25759", VehicleType = "برادو", VehicleColor = "ابيض", VehicleModel = "2011", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "زياد سعيد احمد حسن السماوي", DriverCode = "DR092", PhoneNumber = "772023914", CardNumber = "17010133060", CardType = "شخصية", CardIssueDate = DateTime.Parse("2013-08-22"), LicenseNumber = "23-136830", LicenseIssueDate = DateTime.Parse("2024-09-28"), VehicleNumber = "1-238922", VehicleType = "هيلوكس", VehicleColor = "ابيض", VehicleModel = "2015", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "زياد عزيز ثابت الجبري", DriverCode = "DR091", PhoneNumber = "773831116", CardNumber = "17010237486", CardType = "شخصية", CardIssueDate = DateTime.Parse("2024-05-02"), LicenseNumber = "30-24003", LicenseIssueDate = DateTime.Parse("2024-10-29"), VehicleNumber = "3-111753", VehicleType = "هيلوكس", VehicleColor = "ابيض", VehicleModel = "2013", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "شرف يحيى احمد قايد محرم", DriverCode = "DR093", PhoneNumber = "771816913", CardNumber = "17010111517", CardType = "شخصية", CardIssueDate = DateTime.Parse("2012-08-28"), LicenseNumber = "23-138390", LicenseIssueDate = DateTime.Parse("2024-10-20"), VehicleNumber = "1-239061", VehicleType = "هيلوكس", VehicleColor = "ابيض", VehicleModel = "2013", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "صالح علي حسن العطير", DriverCode = "DR086", PhoneNumber = "772096928", CardNumber = "17010088912", CardType = "شخصية", CardIssueDate = DateTime.Parse("2011-12-09"), LicenseNumber = "9-24595", LicenseIssueDate = DateTime.Parse("2024-05-20"), VehicleNumber = "9-31733", VehicleType = "هيلوكس", VehicleColor = "ابيض", VehicleModel = "2012", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "عبدالالة محمد صالح السويدي", DriverCode = "DR098", PhoneNumber = "777343811", CardNumber = "17010031800", CardType = "شخصية", CardIssueDate = DateTime.Parse("2007-05-29"), LicenseNumber = "3-131570", LicenseIssueDate = DateTime.Parse("2024-01-14"), VehicleNumber = "2-87937", VehicleType = "كنتر", VehicleColor = "ابيض", VehicleModel = "2007", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "عبدالاله محمد صالح السويدي", DriverCode = "DR061", PhoneNumber = "777343811", CardNumber = "17010031800", CardType = "شخصية", CardIssueDate = DateTime.Parse("2007-05-29"), LicenseNumber = "9-17233", LicenseIssueDate = DateTime.Parse("2022-02-13"), VehicleNumber = "9-18093", VehicleType = "فورشنال", VehicleColor = "ابيض", VehicleModel = "2009", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "عبداللطيف محمد علي الجبري", DriverCode = "DR063", PhoneNumber = "770241016", CardNumber = "17010217415", CardType = "شخصية", CardIssueDate = DateTime.Parse("2021-02-16"), LicenseNumber = "1-42474", LicenseIssueDate = DateTime.Parse("2025-02-22"), VehicleNumber = "6-33933", VehicleType = "هيلوكس", VehicleColor = "ابيض", VehicleModel = "2015", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "عبدالوارث علي احمد الخطيب", DriverCode = "DR095", PhoneNumber = "777274037", CardNumber = "01010699271", CardType = "شخصية", CardIssueDate = DateTime.Parse("2023-05-10"), LicenseNumber = "1-61783", LicenseIssueDate = DateTime.Parse("2024-02-27"), VehicleNumber = "3-54313", VehicleType = "فورشنال", VehicleColor = "فضي", VehicleModel = "2020", VehicleCapacity = "6 بسطون", IsActive = true },

                new Driver { Name = "عبده علي محمد عايض", DriverCode = "DR062", PhoneNumber = "771257033", CardNumber = "17010052992", CardType = "شخصية", CardIssueDate = DateTime.Parse("2014-09-10"), LicenseNumber = "3-153168", LicenseIssueDate = DateTime.Parse("2024-09-23"), VehicleNumber = "2-224151", VehicleType = "فورشنال", VehicleColor = "ابيض", VehicleModel = "2013", VehicleCapacity = "6 بسطون", IsActive = true },

                new Driver { Name = "علوان قايد حسين العماد", DriverCode = "DR099", PhoneNumber = "771158879", CardNumber = "-", CardType = "-", CardIssueDate = DateTime.Parse("2025-02-20"), LicenseNumber = "-", LicenseIssueDate = DateTime.Parse("2025-02-20"), VehicleNumber = "-", VehicleType = "فورشنال", VehicleColor = "-", VehicleModel = "-", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "علي احمد محمد المعظم", DriverCode = "DR064", PhoneNumber = "773250700", CardNumber = "17010173930", CardType = "شخصية", CardIssueDate = DateTime.Parse("2015-01-17"), LicenseNumber = "3-8837", LicenseIssueDate = DateTime.Parse("2024-01-14"), VehicleNumber = "3-1648", VehicleType = "فورشنال", VehicleColor = "بني", VehicleModel = "2008", VehicleCapacity = "6 بسطون", IsActive = true },

                new Driver { Name = "فيصل حميد احمد الطيبي", DriverCode = "DR103", PhoneNumber = "775597743", CardNumber = "17010011554", CardType = "شخصية", CardIssueDate = DateTime.Parse("2009-07-12"), LicenseNumber = "3-167502", LicenseIssueDate = DateTime.Parse("2024-08-13"), VehicleNumber = "2-233560", VehicleType = "برادو", VehicleColor = "ابيض لؤلؤي", VehicleModel = "2009", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "مجاهد سنان صالح عبيد", DriverCode = "DR068", PhoneNumber = "775317517", CardNumber = "17010021326", CardType = "شخصية", CardIssueDate = DateTime.Parse("2023-01-04"), LicenseNumber = "23-99936", LicenseIssueDate = DateTime.Parse("2023-06-25"), VehicleNumber = "1-202822", VehicleType = "برادو", VehicleColor = "ابيض", VehicleModel = "2012", VehicleCapacity = "6 بسطون", IsActive = true },

                new Driver { Name = "محمد احمد علي يحيى الجعوري", DriverCode = "DR069", PhoneNumber = "777673632", CardNumber = "17010148844", CardType = "شخصية", CardIssueDate = DateTime.Parse("2014-04-06"), LicenseNumber = "9-17572", LicenseIssueDate = DateTime.Parse("2023-08-14"), VehicleNumber = "9-18748", VehicleType = "فورشنال", VehicleColor = "ابيض", VehicleModel = "2010", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "محمد عبده علي محمد عايض", DriverCode = "DR080", PhoneNumber = "733670852", CardNumber = "17010121494", CardType = "شخصية", CardIssueDate = DateTime.Parse("2013-03-12"), LicenseNumber = "9-8096", LicenseIssueDate = DateTime.Parse("2024-10-01"), VehicleNumber = "9-19764", VehicleType = "هيلوكس", VehicleColor = "أبيض", VehicleModel = "2013", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "محمد مسعود محمد الاهمش", DriverCode = "DR070", PhoneNumber = "777426675", CardNumber = "17010117064", CardType = "شخصية", CardIssueDate = DateTime.Parse("2012-12-22"), LicenseNumber = "9-10423", LicenseIssueDate = DateTime.Parse("2023-06-03"), VehicleNumber = "9-23029", VehicleType = "فورشنال", VehicleColor = "فضي", VehicleModel = "2009", VehicleCapacity = "6 بسطون", IsActive = true },

                new Driver { Name = "محمود علي احمد ناجي الشامي", DriverCode = "DR071", PhoneNumber = "777712605", CardNumber = "08010094601", CardType = "شخصية", CardIssueDate = DateTime.Parse("2014-08-26"), LicenseNumber = "3-30699", LicenseIssueDate = DateTime.Parse("2023-04-10"), VehicleNumber = "2-125722", VehicleType = "هيلوكس", VehicleColor = "ابيض", VehicleModel = "2013", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "مصلح محمد علي محمد الجبري", DriverCode = "DR072", PhoneNumber = "777467099", CardNumber = "17010066770", CardType = "شخصية", CardIssueDate = DateTime.Parse("2022-07-18"), LicenseNumber = "-", LicenseIssueDate = DateTime.Parse("2018-12-11"), VehicleNumber = "9-14494", VehicleType = "هيلوكس", VehicleColor = "ابيض", VehicleModel = "2011", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "منيف احمد مسعد الفاطمي", DriverCode = "DR073", PhoneNumber = "772889972", CardNumber = "17010016963", CardType = "شخصية", CardIssueDate = DateTime.Parse("2022-11-04"), LicenseNumber = "31-7859", LicenseIssueDate = DateTime.Parse("2024-09-09"), VehicleNumber = "2-183096", VehicleType = "هيلوكس", VehicleColor = "ابيض", VehicleModel = "2009", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "ناصر احمد صالح شملي", DriverCode = "DR074", PhoneNumber = "770844427", CardNumber = "17010056708", CardType = "شخصية", CardIssueDate = DateTime.Parse("2014-04-20"), LicenseNumber = "9-16000", LicenseIssueDate = DateTime.Parse("2024-07-23"), VehicleNumber = "9-17259", VehicleType = "فورشنال", VehicleColor = "ابيض", VehicleModel = "2012", VehicleCapacity = "6 بسطون", IsActive = true },

                new Driver { Name = "نبيل سعيد محمد الشامي", DriverCode = "DR087", PhoneNumber = "777842592", CardNumber = "17010097498", CardType = "شخصية", CardIssueDate = DateTime.Parse("2024-08-28"), LicenseNumber = "3-45095", LicenseIssueDate = DateTime.Parse("2024-09-10"), VehicleNumber = "2-135669", VehicleType = "هيلوكس", VehicleColor = "ابيض", VehicleModel = "2013", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "نصر العزي مصلح اللواع", DriverCode = "DR075", PhoneNumber = "770866403", CardNumber = "17010122095", CardType = "شخصية", CardIssueDate = DateTime.Parse("2023-10-03"), LicenseNumber = "23-25386", LicenseIssueDate = DateTime.Parse("2022-07-18"), VehicleNumber = "1-103556", VehicleType = "هيلوكس", VehicleColor = "ابيض", VehicleModel = "2012", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "نصر ناجي قايد السماوي", DriverCode = "DR076", PhoneNumber = "773310870", CardNumber = "17010165982", CardType = "شخصية", CardIssueDate = DateTime.Now, LicenseNumber = "-", LicenseIssueDate = DateTime.Now, VehicleNumber = "19725-9", VehicleType = "هيلوكس", VehicleColor = "ابيض", VehicleModel = "2012", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "نعمان يحيى علي السماوي", DriverCode = "DR088", PhoneNumber = "771811122", CardNumber = "17010115567", CardType = "شخصية", CardIssueDate = DateTime.Parse("2024-09-08"), LicenseNumber = "9-17396", LicenseIssueDate = DateTime.Parse("2022-02-27"), VehicleNumber = "9-18153", VehicleType = "فورشنال", VehicleColor = "بني", VehicleModel = "2018", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "هارون عبدالولي احمد علي العفيري", DriverCode = "DR077", PhoneNumber = "777851492", CardNumber = "17010176874", CardType = "شخصية", CardIssueDate = DateTime.Parse("2021-12-26"), LicenseNumber = "9-21095", LicenseIssueDate = DateTime.Parse("2022-12-12"), VehicleNumber = "9-29642", VehicleType = "فورشنال", VehicleColor = "ابيض", VehicleModel = "2009", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "يحيى محمد يحيى قريمه", DriverCode = "DR085", PhoneNumber = "770307080", CardNumber = "17010361302", CardType = "شخصية", CardIssueDate = DateTime.Now, LicenseNumber = "-", LicenseIssueDate = DateTime.Now, VehicleNumber = "9/77", VehicleType = "حافلة", VehicleColor = "ابيض", VehicleModel = "2005", VehicleCapacity = "4 بسطون", IsActive = true },

                new Driver { Name = "يوسف احمد علي يحيى الجعوري", DriverCode = "DR078", PhoneNumber = "770277059", CardNumber = "17010088326", CardType = "شخصية", CardIssueDate = DateTime.Parse("2011-02-03"), LicenseNumber = "3-15454", LicenseIssueDate = DateTime.Parse("2024-09-11"), VehicleNumber = "2-124311", VehicleType = "برادو", VehicleColor = "ابيض", VehicleModel = "2011", VehicleCapacity = "4 بسطون", IsActive = true }
            };

            int addedDriversCount = 0;
            int addedVehiclesCount = 0;

            foreach (var driver in realDrivers)
            {
                // إضافة السائق
                var driverSuccess = await _dataService.AddDriverAsync(driver);
                if (driverSuccess)
                {
                    addedDriversCount++;

                    // إنشاء سيارة مرتبطة بالسائق
                    var vehicle = new Vehicle
                    {
                        OwnerName = driver.Name,
                        IdCardNumber = driver.CardNumber,
                        VehicleType = driver.VehicleType ?? "غير محدد",
                        PlateNumber = driver.VehicleNumber ?? $"V-{driver.DriverCode}",
                        DriverCode = driver.DriverCode,
                        Model = driver.VehicleModel ?? "غير محدد",
                        Color = driver.VehicleColor ?? "غير محدد",
                        Year = int.TryParse(driver.VehicleModel, out int year) ? year : 2020,
                        SectorId = 1, // القطاع الافتراضي
                        SectorName = "القطاع",
                        CreatedAt = DateTime.Now,
                        IsActive = true
                    };

                    // إضافة السيارة
                    var vehicleSuccess = await _dataService.AddVehicleAsync(vehicle);
                    if (vehicleSuccess)
                    {
                        addedVehiclesCount++;
                        System.Diagnostics.Debug.WriteLine($"✅ تم إضافة السائق والسيارة: {driver.Name} - {vehicle.PlateNumber}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ فشل في إضافة السيارة للسائق: {driver.Name}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ فشل في إضافة السائق: {driver.Name}");
                }
            }

            System.Diagnostics.Debug.WriteLine($"🚗 تم إضافة {addedDriversCount} سائق حقيقي");
            System.Diagnostics.Debug.WriteLine($"🚙 تم إضافة {addedVehiclesCount} سيارة حقيقية");
        }

        public void Dispose()
        {
            _dataService?.Dispose();
        }
    }
}
