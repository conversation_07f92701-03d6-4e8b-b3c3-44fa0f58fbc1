<Window x:Class="DriverManagementSystem.Views.UserPermissionsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة صلاحيات المستخدم"
        Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F8F9FA"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Modern Button Style like Excel Import with Icons -->
        <Style x:Key="FormalButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="6"
                               Padding="{TemplateBinding Padding}"
                               x:Name="border">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock x:Name="IconText" Text="{TemplateBinding Tag}"
                                          FontSize="16" FontWeight="Bold"
                                          Foreground="{TemplateBinding Foreground}"
                                          Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="border" Property="Opacity" Value="0.5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20">
            <StackPanel>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🔑" FontSize="20" Foreground="White" Margin="0,0,10,0" VerticalAlignment="Center"/>
                    <TextBlock Text="إدارة صلاحيات المستخدم" 
                             FontSize="18" 
                             FontWeight="Bold" 
                             Foreground="White"
                             VerticalAlignment="Center"/>
                </StackPanel>
                <TextBlock x:Name="UserInfoText"
                         Text="المستخدم: admin"
                         FontSize="14"
                         Foreground="White"
                         Opacity="0.8"
                         Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Permissions List -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30">
            <StackPanel x:Name="PermissionsPanel">
                <!-- Permissions will be loaded here dynamically -->
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Padding="30,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                <Button x:Name="SaveButton"
                        Content="حفظ الصلاحيات" Tag="💾"
                        Background="{StaticResource PrimaryBrush}"
                        Foreground="White"
                        Padding="20,8"
                        Margin="0,0,15,0"
                        Click="SaveButton_Click"
                        Style="{StaticResource FormalButtonStyle}"/>
                <Button x:Name="SelectAllButton"
                        Content="تحديد الكل" Tag="☑"
                        Background="#4CAF50"
                        Foreground="White"
                        Padding="20,8"
                        Margin="0,0,15,0"
                        Click="SelectAllButton_Click"
                        Style="{StaticResource FormalButtonStyle}"/>
                <Button x:Name="ClearAllButton"
                        Content="إلغاء الكل" Tag="☐"
                        Background="#FF9800"
                        Foreground="White"
                        Padding="20,8"
                        Margin="0,0,15,0"
                        Click="ClearAllButton_Click"
                        Style="{StaticResource FormalButtonStyle}"/>
                <Button x:Name="CancelButton"
                        Content="إغلاق" Tag="✕"
                        Background="#6C757D"
                        Foreground="White"
                        Padding="20,8"
                        Click="CancelButton_Click"
                        Style="{StaticResource FormalButtonStyle}"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
