using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;
using Prism.Commands;

namespace DriverManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel مبسط للتكليف بدون تعقيدات
    /// </summary>
    public class SimpleAssignmentViewModel : INotifyPropertyChanged
    {
        private readonly FieldVisit _selectedVisit;

        public event PropertyChangedEventHandler PropertyChanged;

        // الخصائص الأساسية
        public FieldVisit SelectedVisit => _selectedVisit;
        public ObservableCollection<VisitorWithIndex> Visitors { get; set; }
        public ICommand PrintCommand { get; set; }
        public ICommand CloseCommand { get; set; }

        // خصائص محسوبة
        public string AssignmentNumber => $"تكليف رقم {_selectedVisit?.VisitNumber ?? "001"}/2025";
        public string MissionPurpose => _selectedVisit?.MissionPurpose ?? "زيارة ميدانية للمشاريع";
        public string DepartureDate => _selectedVisit?.DepartureDate.ToString("dd/MM/yyyy") ?? DateTime.Now.ToString("dd/MM/yyyy");
        public string ReturnDate => _selectedVisit?.ReturnDate.ToString("dd/MM/yyyy") ?? DateTime.Now.AddDays(3).ToString("dd/MM/yyyy");
        public string DurationText
        {
            get
            {
                if (_selectedVisit == null) return "3 أيام";
                var duration = (_selectedVisit.ReturnDate - _selectedVisit.DepartureDate).Days + 1;
                return duration == 1 ? "يوم واحد" : duration == 2 ? "يومان" : $"{duration} أيام";
            }
        }
        public string DriverName => _selectedVisit?.SelectedDrivers ?? "أحمد محمد الشامي";
        public string DriverPhoneNumber => "777123456";
        public string VehicleType => "تويوتا هايلكس";

        public SimpleAssignmentViewModel(FieldVisit selectedVisit)
        {
            _selectedVisit = selectedVisit;
            Visitors = new ObservableCollection<VisitorWithIndex>();

            // تهيئة البيانات
            InitializeData();

            // تهيئة الأوامر
            PrintCommand = new DelegateCommand(PrintAssignment);
            CloseCommand = new DelegateCommand<Window>(CloseWindow);
        }

        /// <summary>
        /// تهيئة البيانات المبسطة
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // إضافة القائمين بالزيارة
                Visitors.Clear();

                // إضافة بيانات افتراضية
                Visitors.Add(new VisitorWithIndex
                {
                    Index = 1,
                    Visitor = new FieldVisitor
                    {
                        OfficerName = "أحمد محمد الشامي",
                        OfficerRank = "مدير المشاريع",
                        OfficerCode = "12345678",
                        PhoneNumber = "777123456"
                    }
                });

                Visitors.Add(new VisitorWithIndex
                {
                    Index = 2,
                    Visitor = new FieldVisitor
                    {
                        OfficerName = "فاطمة علي الحميري",
                        OfficerRank = "مسؤولة المتابعة",
                        OfficerCode = "87654321",
                        PhoneNumber = "777654321"
                    }
                });

                // إذا كان لدينا بيانات حقيقية، استخدمها
                if (_selectedVisit?.Visitors?.Any() == true)
                {
                    Visitors.Clear();
                    for (int i = 0; i < _selectedVisit.Visitors.Count; i++)
                    {
                        Visitors.Add(new VisitorWithIndex
                        {
                            Index = i + 1,
                            Visitor = _selectedVisit.Visitors[i]
                        });
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {Visitors.Count} قائم بالزيارة في التكليف المبسط");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة بيانات التكليف المبسط: {ex.Message}");
            }
        }

        /// <summary>
        /// طباعة التكليف
        /// </summary>
        private void PrintAssignment()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ بدء طباعة التكليف المبسط...");

                // البحث عن نافذة التكليف الحالية
                var assignmentWindow = Application.Current.Windows.OfType<Views.SimpleAssignmentWindow>().FirstOrDefault();
                if (assignmentWindow == null)
                {
                    MessageBox.Show("لم يتم العثور على نافذة التكليف للطباعة", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // استخدام خدمة الطباعة المبسطة
                var printDialog = new System.Windows.Controls.PrintDialog();
                
                if (printDialog.ShowDialog() == true)
                {
                    // طباعة النافذة مباشرة
                    printDialog.PrintVisual(assignmentWindow, $"تكليف - زيارة {_selectedVisit?.VisitNumber ?? ""}");

                    System.Diagnostics.Debug.WriteLine("✅ تم إرسال التكليف للطباعة بنجاح");
                    MessageBox.Show("تم إرسال التكليف للطباعة بنجاح", "طباعة",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في طباعة التكليف: {ex.Message}");
                MessageBox.Show($"خطأ في طباعة التكليف: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void CloseWindow(Window window)
        {
            try
            {
                window?.Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إغلاق النافذة: {ex.Message}");
            }
        }

        protected virtual void RaisePropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// كلاس مساعد للقائمين بالزيارة مع الترقيم
    /// </summary>
    public class VisitorWithIndex
    {
        public int Index { get; set; }
        public FieldVisitor Visitor { get; set; }
        
        // خصائص مختصرة للوصول السهل
        public string OfficerName => Visitor?.OfficerName ?? "";
        public string OfficerRank => Visitor?.OfficerRank ?? "";
        public string OfficerCode => Visitor?.OfficerCode ?? "";
        public string PhoneNumber => Visitor?.PhoneNumber ?? "";
    }
}
