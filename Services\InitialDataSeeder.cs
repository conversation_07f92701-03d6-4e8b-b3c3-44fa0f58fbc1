using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة إضافة البيانات الأساسية للنظام
    /// </summary>
    public class InitialDataSeeder
    {
        private readonly ApplicationDbContext _context;

        public InitialDataSeeder(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// إضافة جميع البيانات الأساسية
        /// </summary>
        public async Task SeedBasicDataAsync()
        {
            try
            {
                // إضافة القطاعات
                await SeedSectorsAsync();
                
                // إضافة الموظفين
                await SeedOfficersAsync();
                
                System.Diagnostics.Debug.WriteLine("✅ تم إضافة البيانات الأساسية بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة البيانات الأساسية: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إضافة القطاعات الافتراضية
        /// </summary>
        private async Task SeedSectorsAsync()
        {
            if (await _context.Sectors.AnyAsync())
                return; // القطاعات موجودة بالفعل

            var sectors = new[]
            {
                new Sector { Code = "QTAA", Name = "القطاع", Description = "القطاع العام" },
                new Sector { Code = "training", Name = "التدريب", Description = "قطاع التدريب والتطوير" },
                new Sector { Code = "contracts", Name = "التعاقدات", Description = "قطاع التعاقدات والمشتريات" },
                new Sector { Code = "education", Name = "التعليم", Description = "قطاع التعليم والتربية" },
                new Sector { Code = "empowerment", Name = "التمكين", Description = "قطاع التمكين والتطوير" },
                new Sector { Code = "accounts", Name = "الحسابات", Description = "قطاع الحسابات والمالية" },
                new Sector { Code = "agriculture", Name = "الزراعة", Description = "قطاع الزراعة والثروة الحيوانية" },
                new Sector { Code = "complaints_compliance", Name = "الشكاوى والامتثال", Description = "قطاع الشكاوى والامتثال" },
                new Sector { Code = "health_social_protection", Name = "الصحة والحماية الاجتماعية", Description = "قطاع الصحة والحماية الاجتماعية" },
                new Sector { Code = "roads", Name = "الطرق", Description = "قطاع الطرق والمواصلات" },
                new Sector { Code = "technical", Name = "الفنية", Description = "قطاع الشؤون الفنية" },
                new Sector { Code = "monitoring_evaluation", Name = "المراقبة والتقييم", Description = "قطاع المراقبة والتقييم" },
                new Sector { Code = "water_environment", Name = "المياه والبيئة", Description = "قطاع المياه والبيئة" },
                new Sector { Code = "cash_for_work", Name = "النقد مقابل العمل", Description = "قطاع النقد مقابل العمل" }
            };

            _context.Sectors.AddRange(sectors);
            await _context.SaveChangesAsync();
            
            System.Diagnostics.Debug.WriteLine($"✅ تم إضافة {sectors.Length} قطاع");
        }

        /// <summary>
        /// إضافة الموظفين الافتراضيين
        /// </summary>
        private async Task SeedOfficersAsync()
        {
            if (await _context.Officers.AnyAsync())
                return; // الموظفين موجودين بالفعل

            // التأكد من وجود القطاعات أولاً
            var sectors = await _context.Sectors.ToListAsync();
            if (!sectors.Any())
                return;

            var officers = new[]
            {
                new Officer { Name = "أحمد محمد علي", Rank = "مدير", Code = "EMP001", SectorId = sectors[0].Id },
                new Officer { Name = "فاطمة أحمد حسن", Rank = "نائب مدير", Code = "EMP002", SectorId = sectors[0].Id },
                new Officer { Name = "محمد علي أحمد", Rank = "موظف أول", Code = "EMP003", SectorId = sectors[1].Id },
                new Officer { Name = "عائشة محمد علي", Rank = "موظف", Code = "EMP004", SectorId = sectors[1].Id },
                new Officer { Name = "عبدالله أحمد محمد", Rank = "مدير", Code = "EMP005", SectorId = sectors[2].Id },
                new Officer { Name = "خديجة علي حسن", Rank = "موظف أول", Code = "EMP006", SectorId = sectors[2].Id },
                new Officer { Name = "يوسف محمد أحمد", Rank = "مدير", Code = "EMP007", SectorId = sectors[3].Id },
                new Officer { Name = "مريم أحمد علي", Rank = "موظف", Code = "EMP008", SectorId = sectors[3].Id },
                new Officer { Name = "إبراهيم علي محمد", Rank = "مدير", Code = "EMP009", SectorId = sectors[4].Id },
                new Officer { Name = "زينب محمد أحمد", Rank = "موظف أول", Code = "EMP010", SectorId = sectors[4].Id },
                new Officer { Name = "خالد أحمد علي", Rank = "مدير", Code = "EMP011", SectorId = sectors[5].Id },
                new Officer { Name = "نادية محمد حسن", Rank = "موظف أول", Code = "EMP012", SectorId = sectors[5].Id },
                new Officer { Name = "سعد علي محمد", Rank = "مدير", Code = "EMP013", SectorId = sectors[6].Id },
                new Officer { Name = "هدى أحمد علي", Rank = "موظف", Code = "EMP014", SectorId = sectors[6].Id },
                new Officer { Name = "عمر محمد أحمد", Rank = "مدير", Code = "EMP015", SectorId = sectors[7].Id },
                new Officer { Name = "سلمى علي حسن", Rank = "موظف أول", Code = "EMP016", SectorId = sectors[7].Id },
                new Officer { Name = "طارق أحمد محمد", Rank = "مدير", Code = "EMP017", SectorId = sectors[8].Id },
                new Officer { Name = "ليلى محمد علي", Rank = "موظف", Code = "EMP018", SectorId = sectors[8].Id },
                new Officer { Name = "ماجد علي أحمد", Rank = "مدير", Code = "EMP019", SectorId = sectors[9].Id },
                new Officer { Name = "رانيا محمد حسن", Rank = "موظف أول", Code = "EMP020", SectorId = sectors[9].Id }
            };

            _context.Officers.AddRange(officers);
            await _context.SaveChangesAsync();
            
            System.Diagnostics.Debug.WriteLine($"✅ تم إضافة {officers.Length} موظف");
        }
    }
}
