using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.Migrations
{
    /// <summary>
    /// Migration مباشر لإنشاء جدول MessageDocumentations
    /// </summary>
    public static class CreateMessageDocumentationTableDirect
    {
        public static async Task ApplyAsync(ApplicationDbContext context)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء إنشاء جدول MessageDocumentations مباشرة");

                // إنشاء جدول MessageDocumentations مباشرة
                await context.Database.ExecuteSqlRawAsync(@"
                    CREATE TABLE IF NOT EXISTS MessageDocumentations (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        VisitNumber NVARCHAR(50) NOT NULL,
                        DocumentationDate DATETIME NOT NULL DEFAULT (datetime('now')),
                        ReportNumber NVARCHAR(50),
                        VisitConductor1 NVARCHAR(100),
                        VisitConductor2 NVARCHAR(100),
                        VisitConductor3 NVARCHAR(100),
                        FirstOfficer NVARCHAR(100),
                        SecondOfficer NVARCHAR(100),
                        ThirdOfficer NVARCHAR(100),
                        Notes NVARCHAR(2000),
                        Status NVARCHAR(50) DEFAULT 'مسودة',
                        ImagesFolderPath NVARCHAR(500),
                        ImagePath1 NVARCHAR(500),
                        ImagePath2 NVARCHAR(500),
                        ImagePath3 NVARCHAR(500),
                        ImagePath4 NVARCHAR(500),
                        ImagePath5 NVARCHAR(500),
                        ImagePath6 NVARCHAR(500),
                        ImagesCount INTEGER DEFAULT 0,
                        CreatedDate DATETIME NOT NULL DEFAULT (datetime('now')),
                        LastModified DATETIME NOT NULL DEFAULT (datetime('now'))
                    )");

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء جدول MessageDocumentations");

                // إنشاء جدول MessageAttachments
                await context.Database.ExecuteSqlRawAsync(@"
                    CREATE TABLE IF NOT EXISTS MessageAttachments (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        MessageDocumentationId INTEGER NOT NULL,
                        FileName NVARCHAR(255) NOT NULL,
                        FilePath NVARCHAR(500) NOT NULL,
                        FileType NVARCHAR(50),
                        FileSize INTEGER NOT NULL,
                        Description NVARCHAR(500),
                        AddedDate DATETIME NOT NULL DEFAULT (datetime('now')),
                        FOREIGN KEY (MessageDocumentationId) REFERENCES MessageDocumentations(Id) ON DELETE CASCADE
                    )");

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء جدول MessageAttachments");

                // إنشاء الفهارس
                await context.Database.ExecuteSqlRawAsync(@"
                    CREATE INDEX IF NOT EXISTS IX_MessageDocumentations_VisitNumber 
                    ON MessageDocumentations(VisitNumber)");

                await context.Database.ExecuteSqlRawAsync(@"
                    CREATE INDEX IF NOT EXISTS IX_MessageDocumentations_ReportNumber 
                    ON MessageDocumentations(ReportNumber)");

                await context.Database.ExecuteSqlRawAsync(@"
                    CREATE INDEX IF NOT EXISTS IX_MessageDocumentations_DocumentationDate 
                    ON MessageDocumentations(DocumentationDate)");

                await context.Database.ExecuteSqlRawAsync(@"
                    CREATE INDEX IF NOT EXISTS IX_MessageAttachments_MessageDocumentationId 
                    ON MessageAttachments(MessageDocumentationId)");

                await context.Database.ExecuteSqlRawAsync(@"
                    CREATE INDEX IF NOT EXISTS IX_MessageAttachments_FileType 
                    ON MessageAttachments(FileType)");

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء جميع الفهارس");
                System.Diagnostics.Debug.WriteLine("✅ تم تطبيق Migration بنجاح: جداول توثيق الرسائل");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء جداول توثيق الرسائل: {ex.Message}");
                throw;
            }
        }
    }
}
