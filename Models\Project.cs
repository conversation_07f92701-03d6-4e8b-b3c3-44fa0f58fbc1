using System;
using System.ComponentModel;

namespace DriverManagementSystem.Models
{
    public class Project : INotifyPropertyChanged
    {
        private int _id;
        private string _projectNumber = string.Empty;
        private string _projectName = string.Empty;
        private bool _isActive = true;

        public int Id
        {
            get => _id;
            set
            {
                _id = value;
                OnPropertyChanged(nameof(Id));
            }
        }

        public string ProjectNumber
        {
            get => _projectNumber;
            set
            {
                _projectNumber = value;
                OnPropertyChanged(nameof(ProjectNumber));
            }
        }

        public string ProjectName
        {
            get => _projectName;
            set
            {
                _projectName = value;
                OnPropertyChanged(nameof(ProjectName));
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                _isActive = value;
                OnPropertyChanged(nameof(IsActive));
            }
        }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public string DisplayText => $"{ProjectNumber} - {ProjectName}";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // FieldVisitProject moved to separate file

    public class ProjectInput : INotifyPropertyChanged
    {
        private string _number = string.Empty;
        private string _projectNumber = string.Empty;
        private string _projectName = string.Empty;
        private int _projectDays;
        private bool _isProjectFound = true;

        public string Number
        {
            get => _number;
            set
            {
                _number = value;
                OnPropertyChanged(nameof(Number));
            }
        }

        public string ProjectNumber
        {
            get => _projectNumber;
            set
            {
                _projectNumber = value;
                OnPropertyChanged(nameof(ProjectNumber));
            }
        }

        public string ProjectName
        {
            get => _projectName;
            set
            {
                _projectName = value;
                OnPropertyChanged(nameof(ProjectName));
            }
        }

        public int ProjectDays
        {
            get => _projectDays;
            set
            {
                _projectDays = value;
                OnPropertyChanged(nameof(ProjectDays));
            }
        }

        public bool IsProjectFound
        {
            get => _isProjectFound;
            set
            {
                _isProjectFound = value;
                OnPropertyChanged(nameof(IsProjectFound));
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
