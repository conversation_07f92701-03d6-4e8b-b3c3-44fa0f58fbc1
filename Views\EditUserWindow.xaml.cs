using System;
using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    public partial class EditUserWindow : Window
    {
        private readonly IUserService _userService;
        private readonly User _user;
        public event EventHandler? UserUpdated;

        public EditUserWindow(IUserService userService, User user)
        {
            InitializeComponent();
            _userService = userService;
            _user = user;
            LoadUserData();
        }

        private void LoadUserData()
        {
            FullNameTextBox.Text = _user.FullName;
            UsernameTextBox.Text = _user.Username;
            EmailTextBox.Text = _user.Email;
            IsActiveCheckBox.IsChecked = _user.IsActive;
            NotesTextBox.Text = _user.Notes ?? "";

            // Set role
            foreach (ComboBoxItem item in RoleComboBox.Items)
            {
                if (item.Tag.ToString() == _user.Role)
                {
                    RoleComboBox.SelectedItem = item;
                    break;
                }
            }

            // Disable username editing for admin
            if (_user.Username == "admin")
            {
                UsernameTextBox.IsEnabled = false;
                RoleComboBox.IsEnabled = false;
                IsActiveCheckBox.IsEnabled = false;
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(FullNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الكامل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                FullNameTextBox.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(EmailTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال البريد الإلكتروني", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                EmailTextBox.Focus();
                return;
            }

            // Validate email format
            if (!IsValidEmail(EmailTextBox.Text.Trim()))
            {
                MessageBox.Show("يرجى إدخال بريد إلكتروني صحيح!", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                EmailTextBox.Focus();
                return;
            }

            try
            {
                // Show loading state
                SaveButton.IsEnabled = false;
                SaveButton.Content = "⏳ جاري الحفظ...";
                CancelButton.IsEnabled = false;

                var selectedRole = ((ComboBoxItem)RoleComboBox.SelectedItem).Tag.ToString();

                _user.FullName = FullNameTextBox.Text.Trim();
                _user.Email = EmailTextBox.Text.Trim();
                _user.Role = selectedRole ?? _user.Role;
                _user.IsActive = IsActiveCheckBox.IsChecked ?? true;
                _user.Notes = NotesTextBox.Text.Trim();

                var success = await _userService.UpdateUserAsync(_user);

                if (success)
                {
                    // Update password if provided
                    if (!string.IsNullOrWhiteSpace(PasswordBox.Password))
                    {
                        if (PasswordBox.Password.Length < 6)
                        {
                            MessageBox.Show("كلمة المرور يجب أن تكون 6 أحرف على الأقل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                            PasswordBox.Focus();
                            return;
                        }

                        await _userService.ChangePasswordAsync(_user.UserId, PasswordBox.Password);
                    }

                    MessageBox.Show("تم تحديث المستخدم بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    UserUpdated?.Invoke(this, EventArgs.Empty);
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في تحديث المستخدم.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث المستخدم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // Restore button state
                SaveButton.IsEnabled = true;
                SaveButton.Content = "💾 حفظ التغييرات";
                CancelButton.IsEnabled = true;
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}
